import React from 'react';
import { View, ScrollView } from 'react-native';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { Text } from '../ui/text';
import { Card } from '../ui/card';
import { Coffee, Utensils, Moon, Cookie, Droplet } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

const AnimatedCard = Animated.createAnimatedComponent(Card);

type Meal = Doc<'meal'>;
type Drink = Doc<'drink'>;

interface MealsAndDrinksProps {
  selectedDate: Date;
}

// Helper function to get meal info (color, icon, label)
const getMealInfo = (mealType: 'breakfast' | 'lunch' | 'dinner' | 'snacks') => {
  const mealData = {
    breakfast: {
      color: '#FF8C00',
      icon: Coffee,
      label: 'Breakfast',
      bgColor: '#FFF4E6',
    },
    lunch: {
      color: '#32CD32',
      icon: Utensils,
      label: 'Lunch',
      bgColor: '#F0FFF0',
    },
    dinner: {
      color: '#4169E1',
      icon: Moon,
      label: 'Dinner',
      bgColor: '#F0F8FF',
    },
    snacks: {
      color: '#9932CC',
      icon: Cookie,
      label: 'Snacks',
      bgColor: '#F8F0FF',
    },
  };
  return mealData[mealType];
};

const MealCard = ({ meal, index }: { meal: Meal; index: number }) => {
  const mealInfo = getMealInfo(meal.type);
  const IconComponent = mealInfo.icon;

  return (
    <AnimatedCard
      className="mx-4 mb-3 p-4 bg-white border border-gray-200"
      entering={FadeInDown.delay(index * 100)}
    >
      <View className="flex-row items-center gap-3 mb-2">
        <View
          className="p-2 rounded-full"
          style={{ backgroundColor: mealInfo.bgColor }}
        >
          <IconComponent size={20} color={mealInfo.color} />
        </View>
        <View className="flex-1">
          <Text className="font-semibold text-base text-gray-800">
            {meal.name}
          </Text>
          <Text className="text-sm text-gray-600 capitalize">
            {mealInfo.label}
          </Text>
        </View>
        <Text className="font-bold text-lg" style={{ color: mealInfo.color }}>
          {Math.round(meal.calories)} cal
        </Text>
      </View>
      
      {meal.description && (
        <Text className="text-sm text-gray-600 mb-2">{meal.description}</Text>
      )}
      
      <View className="flex-row justify-between">
        <View className="flex-row gap-4">
          <Text className="text-xs text-gray-500">
            Protein: {Math.round(meal.protein)}g
          </Text>
          <Text className="text-xs text-gray-500">
            Carbs: {Math.round(meal.carbs)}g
          </Text>
          <Text className="text-xs text-gray-500">
            Fat: {Math.round(meal.fat)}g
          </Text>
        </View>
      </View>
    </AnimatedCard>
  );
};

const DrinkCard = ({ drink, index }: { drink: Drink; index: number }) => {
  const mealInfo = getMealInfo(drink.type);

  return (
    <AnimatedCard
      className="mx-4 mb-3 p-4 bg-white border border-gray-200"
      entering={FadeInDown.delay(index * 100)}
    >
      <View className="flex-row items-center gap-3 mb-2">
        <View className="p-2 rounded-full bg-blue-50">
          <Droplet size={20} color="#3B82F6" />
        </View>
        <View className="flex-1">
          <Text className="font-semibold text-base text-gray-800">
            {drink.name}
          </Text>
          <Text className="text-sm text-gray-600 capitalize">
            {mealInfo.label} • Drink
          </Text>
        </View>
        <Text className="font-bold text-lg text-blue-600">
          {Math.round(drink.calories)} cal
        </Text>
      </View>
      
      {drink.description && (
        <Text className="text-sm text-gray-600 mb-2">{drink.description}</Text>
      )}
      
      <View className="flex-row justify-between">
        <View className="flex-row gap-4">
          <Text className="text-xs text-gray-500">
            Protein: {Math.round(drink.protein)}g
          </Text>
          <Text className="text-xs text-gray-500">
            Carbs: {Math.round(drink.carbs)}g
          </Text>
          <Text className="text-xs text-gray-500">
            Fat: {Math.round(drink.fat)}g
          </Text>
        </View>
      </View>
    </AnimatedCard>
  );
};

export function MealsAndDrinks({ selectedDate }: MealsAndDrinksProps) {
  const dateString = selectedDate.toISOString().split('T')[0];
  
  const meals = useQuery(api.meals.getMeals, { date: dateString });
  const drinks = useQuery(api.drinks.getDrinksByDate, { date: dateString });

  const isLoading = meals === undefined || drinks === undefined;

  if (isLoading) {
    return (
      <View className="mx-4 mt-4">
        <Text className="text-center text-gray-500">Loading...</Text>
      </View>
    );
  }

  const totalItems = (meals?.length || 0) + (drinks?.length || 0);

  if (totalItems === 0) {
    return (
      <View className="mx-4 mt-8 p-8">
        <Text className="text-center text-gray-500 text-base">
          No meals or drinks logged for this date
        </Text>
        <Text className="text-center text-gray-400 text-sm mt-2">
          Start tracking your nutrition by adding meals and drinks
        </Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 mt-4" showsVerticalScrollIndicator={false}>
      {/* Summary Header */}
      <View className="mx-4 mb-4 p-4 bg-gray-50 rounded-lg">
        <Text className="font-bold text-lg text-gray-800 mb-1">
          Daily Summary
        </Text>
        <Text className="text-sm text-gray-600">
          {meals?.length || 0} meals • {drinks?.length || 0} drinks
        </Text>
      </View>

      {/* Meals Section */}
      {meals && meals.length > 0 && (
        <View className="mb-4">
          <Text className="font-semibold text-lg text-gray-800 mx-4 mb-3">
            🍽️ Meals
          </Text>
          {meals.map((meal, index) => (
            <MealCard key={meal._id} meal={meal} index={index} />
          ))}
        </View>
      )}

      {/* Drinks Section */}
      {drinks && drinks.length > 0 && (
        <View className="mb-8">
          <Text className="font-semibold text-lg text-gray-800 mx-4 mb-3">
            🥤 Drinks
          </Text>
          {drinks.map((drink, index) => (
            <DrinkCard key={drink._id} drink={drink} index={index} />
          ))}
        </View>
      )}
    </ScrollView>
  );
}
