import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Coffee, Utensils, Moon, <PERSON>ie } from "lucide-react-native";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";

type MealType = "breakfast" | "lunch" | "dinner" | "snacks";

interface MealTimeSelectorProps {
  mealType: MealType;
  onMealTypeChange: (type: MealType) => void;
}

const mealTypeIcons = {
  breakfast: Coffee,
  lunch: Utensils,
  dinner: Moon,
  snacks: <PERSON>ie,
};

export const MealTimeSelector: React.FC<MealTimeSelectorProps> = ({
  mealType,
  onMealTypeChange,
}) => {
  return (
    <Card className="my-6 border-0">
      <View className="flex flex-wrap flex-row gap-3">
        {Object.entries(mealTypeIcons).map(([type, Icon]) => (
          <TouchableOpacity
            key={type}
            onPress={() => onMealTypeChange(type as MealType)}
            className={`w-[48%] p-4 rounded-lg border-2 ${
              mealType === type
                ? "border-primary bg-primary text-white"
                : "border-border bg-background"
            }`}
          >
            <View className="items-center">
              <Icon
                size={24}
                color={mealType === type ? "white" : "black"}
                className={
                  mealType === type ? "text-white" : "text-muted-foreground"
                }
              />
              <Text
                className={`mt-2 capitalize ${
                  mealType === type
                    ? "text-white font-semibold"
                    : "text-muted-foreground"
                }`}
              >
                {type}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );
};
