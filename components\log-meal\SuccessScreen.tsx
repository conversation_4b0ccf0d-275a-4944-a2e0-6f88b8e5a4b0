import React from "react";
import { View } from "react-native";
import { Check } from "lucide-react-native";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";

interface SuccessScreenProps {
  logType: "meal" | "drink";
  onLogAnother: () => void;
  onGoBack: () => void;
}

export const SuccessScreen: React.FC<SuccessScreenProps> = ({
  logType,
  onLogAnother,
  onGoBack,
}) => {
  return (
    <View className="flex-1 p-6 justify-center items-center">
      <View className="items-center mb-8">
        <View className="w-20 h-20 bg-green-100 rounded-full items-center justify-center mb-4">
          <Check size={40} className="text-green-600" />
        </View>
        <Text className="text-2xl font-bold mb-2">Successfully Logged!</Text>
        <Text className="text-muted-foreground text-center">
          Your {logType} has been added to your nutrition tracking.
        </Text>
      </View>

      <View className="w-full space-y-3">
        <Button onPress={onLogAnother} size="lg" className="w-full">
          <Text className="text-white font-semibold">
            Log Another {logType}
          </Text>
        </Button>
        <Button
          onPress={onGoBack}
          variant="outline"
          size="lg"
          className="w-full"
        >
          <Text>Back to Health</Text>
        </Button>
      </View>
    </View>
  );
};
