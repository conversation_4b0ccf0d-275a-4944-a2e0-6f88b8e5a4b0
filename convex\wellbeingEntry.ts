import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Wellbeing Entry Mutations
export const createWellbeingEntry = mutation({
  args: {
    date: v.string(),
    stress: v.optional(v.number()),
    anxiety: v.optional(v.number()),
    happiness: v.optional(v.number()),
    energy: v.optional(v.number()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Check if entry already exists for this date
    const existingEntry = await ctx.db
      .query('wellbeingEntry')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.eq(q.field('date'), args.date))
      .first();

    if (existingEntry) {
      // Update existing entry
      await ctx.db.patch(existingEntry._id, {
        stress: args.stress,
        anxiety: args.anxiety,
        happiness: args.happiness,
        energy: args.energy,
        notes: args.notes,
      });
      return existingEntry._id;
    } else {
      // Create new entry
      const entryId = await ctx.db.insert('wellbeingEntry', {
        ...args,
        userId: identity.subject,
      });
      return entryId;
    }
  },
});

export const updateWellbeingEntry = mutation({
  args: {
    id: v.id('wellbeingEntry'),
    updates: v.object({
      stress: v.optional(v.number()),
      anxiety: v.optional(v.number()),
      happiness: v.optional(v.number()),
      energy: v.optional(v.number()),
      notes: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const entry = await ctx.db.get(args.id);
    if (!entry) {
      throw new Error('Wellbeing entry not found');
    }

    if (entry.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const deleteWellbeingEntry = mutation({
  args: { id: v.id('wellbeingEntry') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const entry = await ctx.db.get(args.id);
    if (!entry) {
      throw new Error('Wellbeing entry not found');
    }

    if (entry.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Wellbeing Entry Queries
export const getWellbeingEntries = query({
  args: {
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    let query = ctx.db
      .query('wellbeingEntry')
      .filter((q) => q.eq(q.field('userId'), identity.subject));

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field('date'), args.startDate!));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field('date'), args.endDate!));
    }

    return await query.order('desc').collect();
  },
});

export const getWellbeingEntryByDate = query({
  args: { date: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return await ctx.db
      .query('wellbeingEntry')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.eq(q.field('date'), args.date))
      .first();
  },
});

export const getWellbeingSummary = query({
  args: {
    startDate: v.string(),
    endDate: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const entries = await ctx.db
      .query('wellbeingEntry')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.gte(q.field('date'), args.startDate))
      .filter((q) => q.lte(q.field('date'), args.endDate))
      .collect();

    if (entries.length === 0) {
      return {
        totalEntries: 0,
        averageStress: 0,
        averageAnxiety: 0,
        averageHappiness: 0,
        averageEnergy: 0,
      };
    }

    const validStress = entries.filter(e => e.stress !== undefined);
    const validAnxiety = entries.filter(e => e.anxiety !== undefined);
    const validHappiness = entries.filter(e => e.happiness !== undefined);
    const validEnergy = entries.filter(e => e.energy !== undefined);

    return {
      totalEntries: entries.length,
      averageStress: validStress.length > 0 
        ? validStress.reduce((sum, e) => sum + e.stress!, 0) / validStress.length 
        : 0,
      averageAnxiety: validAnxiety.length > 0 
        ? validAnxiety.reduce((sum, e) => sum + e.anxiety!, 0) / validAnxiety.length 
        : 0,
      averageHappiness: validHappiness.length > 0 
        ? validHappiness.reduce((sum, e) => sum + e.happiness!, 0) / validHappiness.length 
        : 0,
      averageEnergy: validEnergy.length > 0 
        ? validEnergy.reduce((sum, e) => sum + e.energy!, 0) / validEnergy.length 
        : 0,
    };
  },
});

export const getCurrentWellbeingEntry = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const today = new Date().toISOString().split('T')[0];
    
    return await ctx.db
      .query('wellbeingEntry')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.eq(q.field('date'), today))
      .first();
  },
});
