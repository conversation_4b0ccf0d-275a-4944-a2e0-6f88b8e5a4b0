import React from "react";
import { View, Text } from "react-native";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Moon, Heart, Activity } from "lucide-react-native";

interface MetricCardProps {
  title: string;
  value: string | number;
  unit?: string;
  icon: any;
  color: string;
}

function MetricCard({
  title,
  value,
  unit,
  icon: Icon,
  color,
}: MetricCardProps) {
  return (
    <View className="bg-gray-50 rounded-2xl p-4 shadow-sm border border-border flex-1 mx-1">
      <View className="items-center">
        <View
          className="w-10 h-10 rounded-xl items-center justify-center mb-3"
          style={{ backgroundColor: `${color}20` }}
        >
          <Icon size={20} color={color} />
        </View>
        <Text className="text-lg font-bold text-gray-900 mb-1">
          {value}
          {unit && <Text className="text-lg text-gray-500">{unit}</Text>}
        </Text>
        <Text className="text-muted-foreground text-sm text-center">
          {title}
        </Text>
      </View>
    </View>
  );
}

export function WellbeingMetrics() {
  const lastSleepEntry = useQuery(api.sleep.getLastSleepEntry);
  const currentMood = useQuery(api.mood.getCurrentMood);
  const currentWellbeingEntry = useQuery(
    api.wellbeingEntry.getCurrentWellbeingEntry
  );

  const getMoodColor = (mood: string) => {
    const colors: Record<string, string> = {
      happy: "#22c55e",
      excited: "#f59e0b",
      calm: "#06b6d4",
      neutral: "#6b7280",
      tired: "#8b5cf6",
      stressed: "#ef4444",
      anxious: "#f97316",
      sad: "#3b82f6",
      angry: "#dc2626",
      other: "#64748b",
    };
    return colors[mood] || "#6b7280";
  };

  return (
    <View className="px-6 mb-6">
      <Text className="text-xl font-semibold text-gray-900 mb-4">
        Today's Overview
      </Text>
      <View className="flex-row">
        <MetricCard
          title="Sleep Quality"
          value={lastSleepEntry?.qualityRating || "Not set"}
          unit={lastSleepEntry?.qualityRating ? "/5" : ""}
          icon={Moon}
          color="#8b5cf6"
        />
        <MetricCard
          title="Current Mood"
          value={
            currentMood?.mood
              ? currentMood.mood.charAt(0).toUpperCase() +
                currentMood.mood.slice(1)
              : "Not set"
          }
          icon={Heart}
          color={currentMood?.mood ? getMoodColor(currentMood.mood) : "#6b7280"}
        />
        <MetricCard
          title="Energy Level"
          value={currentWellbeingEntry?.energy || "Not set"}
          unit={currentWellbeingEntry?.energy ? "/10" : ""}
          icon={Activity}
          color="#ef4444"
        />
      </View>
    </View>
  );
}
