import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Wellbeing Streaks Mutations
export const initializeStreak = mutation({
  args: {
    type: v.union(
      v.literal("mood_logging"),
      v.literal("sleep_logging"),
      v.literal("meditation"),
      v.literal("wellbeing_check")
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if streak already exists
    const existingStreak = await ctx.db
      .query("wellbeingStreak")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .filter((q) => q.eq(q.field("type"), args.type))
      .first();

    if (existingStreak) {
      return existingStreak._id;
    }

    const streakId = await ctx.db.insert("wellbeingStreak", {
      userId: identity.subject,
      type: args.type,
      currentStreak: 0,
      longestStreak: 0,
      lastActivityDate: "",
      isActive: false,
    });

    return streakId;
  },
});

export const updateStreak = mutation({
  args: {
    type: v.union(
      v.literal("mood_logging"),
      v.literal("sleep_logging"),
      v.literal("meditation"),
      v.literal("wellbeing_check")
    ),
    activityDate: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get or create streak
    let streak = await ctx.db
      .query("wellbeingStreak")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .filter((q) => q.eq(q.field("type"), args.type))
      .first();

    if (!streak) {
      const streakId = await ctx.db.insert("wellbeingStreak", {
        userId: identity.subject,
        type: args.type,
        currentStreak: 0,
        longestStreak: 0,
        lastActivityDate: "",
        isActive: false,
      });
      streak = await ctx.db.get(streakId);
      if (!streak) throw new Error("Failed to create streak");
    }

    const activityDate = new Date(args.activityDate);
    const lastActivityDate = streak.lastActivityDate
      ? new Date(streak.lastActivityDate)
      : null;

    let newCurrentStreak = streak.currentStreak;
    let newLongestStreak = streak.longestStreak;
    let isActive = true;

    if (!lastActivityDate) {
      // First activity
      newCurrentStreak = 1;
    } else {
      const daysDifference = Math.floor(
        (activityDate.getTime() - lastActivityDate.getTime()) /
          (1000 * 60 * 60 * 24)
      );

      if (daysDifference === 1) {
        // Consecutive day
        newCurrentStreak += 1;
      } else if (daysDifference === 0) {
        // Same day, no change to streak
        // Keep current streak
      } else {
        // Streak broken
        newCurrentStreak = 1;
      }
    }

    // Update longest streak if current is higher
    if (newCurrentStreak > newLongestStreak) {
      newLongestStreak = newCurrentStreak;
    }

    await ctx.db.patch(streak._id, {
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      lastActivityDate: args.activityDate,
      isActive,
    });

    return {
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      isNewRecord:
        newCurrentStreak === newLongestStreak && newCurrentStreak > 1,
    };
  },
});

export const checkAndUpdateStreaks = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const today = new Date().toISOString().split("T")[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0];

    // Get all user streaks
    const streaks = await ctx.db
      .query("wellbeingStreak")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .collect();

    for (const streak of streaks) {
      if (
        streak.lastActivityDate &&
        streak.lastActivityDate < yesterday &&
        streak.isActive
      ) {
        // Streak is broken - reset current streak but keep longest
        await ctx.db.patch(streak._id, {
          currentStreak: 0,
          isActive: false,
        });
      }
    }

    return { success: true };
  },
});

// Wellbeing Streaks Queries
export const getWellbeingStreaks = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("wellbeingStreak")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .collect();
  },
});

export const getStreakByType = query({
  args: {
    type: v.union(
      v.literal("mood_logging"),
      v.literal("sleep_logging"),
      v.literal("meditation"),
      v.literal("wellbeing_check")
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("wellbeingStreak")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .filter((q) => q.eq(q.field("type"), args.type))
      .first();
  },
});

export const getStreakSummary = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const streaks = await ctx.db
      .query("wellbeingStreak")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .collect();

    const summary = {
      totalActiveStreaks: 0,
      longestCurrentStreak: 0,
      longestCurrentStreakType: "",
      totalLongestStreak: 0,
      totalLongestStreakType: "",
      streaksByType: {} as Record<
        string,
        { current: number; longest: number; isActive: boolean }
      >,
    };

    for (const streak of streaks) {
      summary.streaksByType[streak.type] = {
        current: streak.currentStreak,
        longest: streak.longestStreak,
        isActive: streak.isActive,
      };

      if (streak.isActive && streak.currentStreak > 0) {
        summary.totalActiveStreaks++;
      }

      if (streak.currentStreak > summary.longestCurrentStreak) {
        summary.longestCurrentStreak = streak.currentStreak;
        summary.longestCurrentStreakType = streak.type;
      }

      if (streak.longestStreak > summary.totalLongestStreak) {
        summary.totalLongestStreak = streak.longestStreak;
        summary.totalLongestStreakType = streak.type;
      }
    }

    return summary;
  },
});

export const getStreakAchievements = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const streaks = await ctx.db
      .query("wellbeingStreak")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .collect();

    const achievements = [];

    for (const streak of streaks) {
      // Define achievement milestones
      const milestones = [3, 7, 14, 30, 60, 100];

      for (const milestone of milestones) {
        if (streak.longestStreak >= milestone) {
          achievements.push({
            type: streak.type,
            milestone,
            achieved: true,
            title: `${milestone} Day ${streak.type.replace("_", " ")} Streak`,
            description: `Maintained a ${milestone}-day streak in ${streak.type.replace("_", " ")}`,
          });
        }
      }
    }

    return achievements.sort((a, b) => b.milestone - a.milestone);
  },
});
