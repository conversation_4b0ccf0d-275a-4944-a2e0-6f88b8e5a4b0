import React from "react";
import { View, ScrollView, Image } from "react-native";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { NutritionAnalysis } from "@/utils/nutritionAnalysis";

interface NutritionReviewScreenProps {
  selectedImage: string | null;
  nutritionData: NutritionAnalysis;
  logType: "meal" | "drink";
  isLogging: boolean;
  onNutritionDataChange: (data: NutritionAnalysis) => void;
  onBack: () => void;
  onLogMeal: () => void;
}

export const NutritionReviewScreen: React.FC<NutritionReviewScreenProps> = ({
  selectedImage,
  nutritionData,
  logType,
  isLogging,
  onNutritionDataChange,
  onBack,
  onLogMeal,
}) => {
  const updateNutritionData = (field: keyof NutritionAnalysis, value: any) => {
    onNutritionDataChange({ ...nutritionData, [field]: value });
  };

  return (
    <View className="flex-1 p-6">
      <Text className="text-2xl font-bold mb-2">Review Nutrition</Text>
      <Text className="text-muted-foreground mb-6">
        Review and edit the nutrition information before logging.
      </Text>

      {selectedImage && (
        <Card className="p-4 mb-6">
          <Image
            source={{ uri: selectedImage }}
            className="w-full h-32 rounded-lg"
            resizeMode="cover"
          />
        </Card>
      )}

      <ScrollView
        className="flex-1 mb-6"
        showsVerticalScrollIndicator={false}
      >
        <Card className="p-6 mb-4">
          <Text className="text-lg font-semibold mb-4">
            Basic Information
          </Text>
          <View className="space-y-4">
            <View>
              <Text className="text-sm font-medium mb-2">Name</Text>
              <Input
                value={nutritionData.name}
                onChangeText={(text) => updateNutritionData("name", text)}
                placeholder="Enter meal name"
              />
            </View>
            <View>
              <Text className="text-sm font-medium mb-2">Description</Text>
              <Textarea
                value={nutritionData.description}
                onChangeText={(text) => updateNutritionData("description", text)}
                placeholder="Enter description"
                numberOfLines={3}
              />
            </View>
          </View>
        </Card>

        <Card className="p-6 mb-4">
          <Text className="text-lg font-semibold mb-4">Macronutrients</Text>
          <View className="grid grid-cols-2 gap-4">
            <View>
              <Text className="text-sm font-medium mb-2">Calories</Text>
              <Input
                value={nutritionData.calories.toString()}
                onChangeText={(text) =>
                  updateNutritionData("calories", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            <View>
              <Text className="text-sm font-medium mb-2">Protein (g)</Text>
              <Input
                value={nutritionData.protein.toString()}
                onChangeText={(text) =>
                  updateNutritionData("protein", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            <View>
              <Text className="text-sm font-medium mb-2">Carbs (g)</Text>
              <Input
                value={nutritionData.carbs.toString()}
                onChangeText={(text) =>
                  updateNutritionData("carbs", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            <View>
              <Text className="text-sm font-medium mb-2">Fat (g)</Text>
              <Input
                value={nutritionData.fat.toString()}
                onChangeText={(text) =>
                  updateNutritionData("fat", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
          </View>
        </Card>

        <Card className="p-6">
          <Text className="text-lg font-semibold mb-4">
            Additional Nutrients
          </Text>
          <View className="grid grid-cols-2 gap-4">
            <View>
              <Text className="text-sm font-medium mb-2">Fiber (g)</Text>
              <Input
                value={nutritionData.fiber?.toString() || ""}
                onChangeText={(text) =>
                  updateNutritionData("fiber", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            <View>
              <Text className="text-sm font-medium mb-2">Sugar (g)</Text>
              <Input
                value={nutritionData.sugar?.toString() || ""}
                onChangeText={(text) =>
                  updateNutritionData("sugar", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            <View>
              <Text className="text-sm font-medium mb-2">Sodium (mg)</Text>
              <Input
                value={nutritionData.sodium?.toString() || ""}
                onChangeText={(text) =>
                  updateNutritionData("sodium", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            <View>
              <Text className="text-sm font-medium mb-2">Water (ml)</Text>
              <Input
                value={nutritionData.water?.toString() || ""}
                onChangeText={(text) =>
                  updateNutritionData("water", parseFloat(text) || 0)
                }
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
          </View>
        </Card>
      </ScrollView>

      <View className="flex-row space-x-3">
        <Button
          onPress={onBack}
          variant="outline"
          size="lg"
          className="flex-1"
        >
          <Text>Back</Text>
        </Button>
        <Button
          onPress={onLogMeal}
          disabled={isLogging}
          size="lg"
          className="flex-1"
        >
          <Text className="text-white font-semibold">
            {isLogging ? "Logging..." : `Log ${logType}`}
          </Text>
        </Button>
      </View>
    </View>
  );
};
