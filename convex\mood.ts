import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Mood Entry Queries
export const getMoodEntries = query({
  args: {
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query = ctx.db
      .query("moodEntry")
      .filter((q) => q.eq(q.field("userId"), identity.subject));

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("date"), args.startDate!));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("date"), args.endDate!));
    }

    return await query.order("desc").collect();
  },
});

export const getMoodEntryById = query({
  args: { id: v.id("moodEntry") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const moodEntry = await ctx.db.get(args.id);
    if (!moodEntry) {
      throw new Error("Mood entry not found");
    }

    if (moodEntry.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    return moodEntry;
  },
});

export const getMoodSummary = query({
  args: {
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query = ctx.db
      .query("moodEntry")
      .filter((q) => q.eq(q.field("userId"), identity.subject));

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("date"), args.startDate!));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("date"), args.endDate!));
    }

    const moodEntries = await query.collect();

    const moodCounts = moodEntries.reduce(
      (acc, entry) => {
        acc[entry.mood] = (acc[entry.mood] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const totalIntensity = moodEntries.reduce(
      (sum, entry) => sum + (entry.intensity || 5),
      0
    );
    const averageIntensity =
      moodEntries.length > 0 ? totalIntensity / moodEntries.length : 0;

    return {
      totalEntries: moodEntries.length,
      moodCounts,
      averageIntensity,
    };
  },
});

export const getOverallMoodAnalysis = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const moodEntries = await ctx.db
      .query("moodEntry")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .order("desc")
      .take(400);

    if (moodEntries.length === 0) {
      return {
        mostFrequentMood: null,
        moodDistribution: {},
      };
    }

    const moodCounts: Record<string, number> = {};

    for (const entry of moodEntries) {
      moodCounts[entry.mood] = (moodCounts[entry.mood] || 0) + 1;
    }

    let mostFrequentMood: string | null = null;
    let maxCount = 0;
    for (const mood in moodCounts) {
      if (moodCounts[mood] > maxCount) {
        maxCount = moodCounts[mood];
        mostFrequentMood = mood;
      }
    }

    return {
      mostFrequentMood,
      moodDistribution: moodCounts,
    };
  },
});

export const getCurrentMood = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const latestMoodEntry = await ctx.db
      .query("moodEntry")
      .filter((q) => q.eq(q.field("userId"), identity.subject))
      .order("desc")
      .first(); // Get the most recent entry

    return latestMoodEntry;
  },
});

// Mood Entry Mutations
export const createMoodEntry = mutation({
  args: {
    date: v.string(),
    mood: v.union(
      v.literal("happy"),
      v.literal("sad"),
      v.literal("anxious"),
      v.literal("angry"),
      v.literal("neutral"),
      v.literal("excited"),
      v.literal("tired"),
      v.literal("stressed"),
      v.literal("calm"),
      v.literal("other")
    ),
    intensity: v.optional(v.number()),
    note: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const moodEntryId = await ctx.db.insert("moodEntry", {
      ...args,
      userId: identity.subject,
    });

    return moodEntryId;
  },
});

export const updateMoodEntry = mutation({
  args: {
    id: v.id("moodEntry"),
    updates: v.object({
      date: v.optional(v.string()),
      mood: v.optional(
        v.union(
          v.literal("happy"),
          v.literal("sad"),
          v.literal("anxious"),
          v.literal("angry"),
          v.literal("neutral"),
          v.literal("excited"),
          v.literal("tired"),
          v.literal("stressed"),
          v.literal("calm"),
          v.literal("other")
        )
      ),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const moodEntry = await ctx.db.get(args.id);
    if (!moodEntry) {
      throw new Error("Mood entry not found");
    }

    if (moodEntry.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const deleteMoodEntry = mutation({
  args: { id: v.id("moodEntry") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const moodEntry = await ctx.db.get(args.id);
    if (!moodEntry) {
      throw new Error("Mood entry not found");
    }

    if (moodEntry.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});
