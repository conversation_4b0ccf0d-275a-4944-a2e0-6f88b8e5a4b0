import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Coffee, ForkKnifeIcon } from "lucide-react-native";

type LogType = "meal" | "drink";

interface LogTypeSelectorProps {
  logType: LogType;
  onLogTypeChange: (type: LogType) => void;
}

export const LogTypeSelector: React.FC<LogTypeSelectorProps> = ({
  logType,
  onLogTypeChange,
}) => {
  return (
    <Card className="mb-4 border-0">
      <Text className="text-lg font-semibold mb-3">Type</Text>
      <View className="flex-row gap-3">
        <TouchableOpacity
          onPress={() => onLogTypeChange("meal")}
          className={`flex-1 p-3 flex-col items-center gap-3 rounded-lg border-2 ${
            logType === "meal"
              ? "border-primary bg-primary"
              : "border-border bg-background"
          }`}
        >
          <ForkKnifeIcon
            size={18}
            color={logType === "meal" ? "white" : "black"}
          />
          <Text
            className={`text-center font-medium ${
              logType === "meal" ? "text-white" : "text-muted-foreground"
            }`}
          >
            Meal
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => onLogTypeChange("drink")}
          className={`flex-1 p-3 flex-col items-center gap-3 rounded-lg border-2 ${
            logType === "drink"
              ? "border-primary bg-primary"
              : "border-border bg-background"
          }`}
        >
          <Coffee size={18} color={logType === "drink" ? "white" : "black"} />
          <Text
            className={`text-center font-medium ${
              logType === "drink" ? "text-white" : "text-muted-foreground"
            }`}
          >
            Drink
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );
};
