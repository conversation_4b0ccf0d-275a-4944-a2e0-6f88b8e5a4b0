import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Workout Queries
export const getWorkouts = query({
  args: {
    completed: v.optional(v.boolean()),
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query = ctx.db
      .query("workout")
      .filter((q) => q.eq(q.field("userId"), identity.subject));

    if (args.completed !== undefined) {
      query = query.filter((q) => q.eq(q.field("completed"), args.completed));
    }

    if (args.startDate) {
      query = query.filter((q) =>
        q.gte(q.field("scheduledDate"), args.startDate!)
      );
    }

    if (args.endDate) {
      const endOfDay = new Date(args.endDate);
      endOfDay.setHours(23, 59, 59, 999);
      query = query.filter(
        (q) => q.lte(q.field("scheduledDate"), endOfDay.toISOString()) // toISOString() already returns string, no need for !
      );
    }

    return await query.order("desc").collect();
  },
});

export const getWorkoutById = query({
  args: { id: v.id("workout") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error("Workout not found");
    }

    if (workout.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    return workout;
  },
});

export const getWeeklyStats = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get start of current week (Sunday)
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    const workouts = await ctx.db
      .query("workout")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), identity.subject),
          q.gte(q.field("scheduledDate"), startOfWeek.toISOString()),
          q.lte(q.field("scheduledDate"), endOfWeek.toISOString())
        )
      )
      .collect();

    const completedWorkoutsCount = workouts.filter((w) => w.completed).length;
    const totalPlanned = workouts.length;

    // Calculate streak (consecutive days with completed workouts)
    // Improved algorithm that looks back further and handles edge cases better
    let streakDays = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day

    // Get all completed workouts sorted by completion date
    const completedWorkoutsWithDates = workouts
      .filter((w) => w.completed && w.completedAt)
      .map((w) => {
        const completedDate = new Date(w.completedAt!);
        completedDate.setHours(0, 0, 0, 0);
        return {
          ...w,
          completedDateNormalized: completedDate,
          completedDateString: completedDate.toISOString().split("T")[0],
        };
      })
      .sort(
        (a, b) =>
          b.completedDateNormalized.getTime() -
          a.completedDateNormalized.getTime()
      );

    // Group workouts by completion date
    const workoutsByDate = new Map<string, typeof completedWorkoutsWithDates>();
    completedWorkoutsWithDates.forEach((workout) => {
      const dateString = workout.completedDateString;
      if (!workoutsByDate.has(dateString)) {
        workoutsByDate.set(dateString, []);
      }
      workoutsByDate.get(dateString)!.push(workout);
    });

    // Calculate streak starting from today going backwards
    let currentDate = new Date(today);
    let consecutiveDays = 0;
    const maxLookbackDays = 365; // Look back up to a year for streak calculation

    for (let i = 0; i < maxLookbackDays; i++) {
      const dateString = currentDate.toISOString().split("T")[0];
      const hasWorkoutOnDate = workoutsByDate.has(dateString);

      if (hasWorkoutOnDate) {
        consecutiveDays++;
      } else {
        // If this is today and no workout, don't break streak yet (day isn't over)
        if (i === 0) {
          // Check if there are any workouts scheduled for today that could still be completed
          const todayScheduled = workouts.some(
            (w) => !w.completed && w.scheduledDate.startsWith(dateString)
          );
          if (!todayScheduled) {
            // No workouts scheduled for today, so streak is broken
            break;
          }
        } else {
          // For past days, break the streak
          break;
        }
      }

      // Move to previous day
      currentDate.setDate(currentDate.getDate() - 1);
    }

    streakDays = consecutiveDays;

    // Estimate calories burned (rough calculation)
    const caloriesBurned = workouts
      .filter((w) => w.completed)
      .reduce((total, w) => {
        // Rough estimate: 5-10 calories per minute based on workout type
        const multiplier = w.type === "HIIT" ? 10 : w.type === "Cardio" ? 8 : 5;
        return total + w.estimatedDuration * multiplier;
      }, 0);

    return {
      completedWorkouts: completedWorkoutsCount,
      totalPlanned,
      streakDays,
      caloriesBurned,
    };
  },
});

// Workout Mutations
export const createWorkout = mutation({
  args: {
    name: v.string(),
    type: v.string(),
    description: v.optional(v.string()),
    scheduledDate: v.string(),
    estimatedDuration: v.number(),
    exercises: v.array(
      v.object({
        name: v.string(),
        sets: v.number(),
        reps: v.number(),
        weight: v.optional(v.number()),
        duration: v.optional(v.number()),
        restTime: v.optional(v.number()),
      })
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const workoutId = await ctx.db.insert("workout", {
      ...args,
      userId: identity.subject,
      completed: false,
      completedAt: undefined,
    });

    return workoutId;
  },
});

export const updateWorkout = mutation({
  args: {
    id: v.id("workout"),
    updates: v.object({
      name: v.optional(v.string()),
      type: v.optional(v.string()),
      description: v.optional(v.string()),
      scheduledDate: v.optional(v.string()),
      estimatedDuration: v.optional(v.number()),
      exercises: v.optional(
        v.array(
          v.object({
            name: v.string(),
            sets: v.number(),
            reps: v.number(),
            weight: v.optional(v.number()),
            duration: v.optional(v.number()),
            restTime: v.optional(v.number()),
          })
        )
      ),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error("Workout not found");
    }

    if (workout.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const completeWorkout = mutation({
  args: { id: v.id("workout") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error("Workout not found");
    }

    if (workout.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    await ctx.db.patch(args.id, {
      completed: true,
      completedAt: new Date().toISOString(),
    });

    return { success: true };
  },
});

export const deleteWorkout = mutation({
  args: { id: v.id("workout") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error("Workout not found");
    }

    if (workout.userId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Notification-related queries
export const getUpcomingWorkouts = query({
  args: {
    days: v.optional(v.number()), // Number of days to look ahead
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const daysAhead = args.days || 7;
    const now = new Date();
    const futureDate = new Date(now);
    futureDate.setDate(now.getDate() + daysAhead);

    const workouts = await ctx.db
      .query("workout")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), identity.subject),
          q.eq(q.field("completed"), false),
          q.gte(q.field("scheduledDate"), now.toISOString()),
          q.lte(q.field("scheduledDate"), futureDate.toISOString())
        )
      )
      .order("asc")
      .collect();

    return workouts;
  },
});

export const getCompletedWorkoutsCount = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    const workouts = await ctx.db
      .query("workout")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), identity.subject),
          q.eq(q.field("completed"), true)
        )
      )
      .collect();
    return workouts.length;
  },
});
