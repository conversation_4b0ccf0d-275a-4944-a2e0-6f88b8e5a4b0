// Nutrition Analysis Utility
// This is a placeholder for AI-powered nutrition analysis
// In production, this would integrate with services like:
// - OpenAI Vision API
// - Google Cloud Vision API
// - Custom trained models
// - Nutrition databases like USDA FoodData Central

export interface NutritionAnalysis {
  name: string;
  description: string;
  calories: number;
  protein: number; // grams
  carbs: number; // grams
  fat: number; // grams
  fiber?: number; // grams
  sugar?: number; // grams
  sodium?: number; // mg
  water?: number; // ml
  calcium?: number; // mg
  iron?: number; // mg
  vitaminC?: number; // mg
  vitaminD?: number; // IU
  confidence: number; // 0-1 confidence score
  ingredients?: string[];
}

// Mock nutrition data for common foods
const MOCK_NUTRITION_DATABASE = {
  // Meals
  'sandwich': {
    name: 'Sandwich',
    description: 'A delicious sandwich with various fillings',
    calories: 350,
    protein: 18,
    carbs: 35,
    fat: 15,
    fiber: 4,
    sugar: 6,
    sodium: 650,
    water: 120,
    calcium: 150,
    iron: 3,
    vitaminC: 8,
    vitaminD: 25,
  },
  'salad': {
    name: 'Fresh Salad',
    description: 'A healthy mixed green salad',
    calories: 180,
    protein: 8,
    carbs: 12,
    fat: 12,
    fiber: 6,
    sugar: 8,
    sodium: 320,
    water: 200,
    calcium: 120,
    iron: 2,
    vitaminC: 25,
    vitaminD: 15,
  },
  'pasta': {
    name: 'Pasta Dish',
    description: 'Pasta with sauce and toppings',
    calories: 450,
    protein: 16,
    carbs: 65,
    fat: 14,
    fiber: 5,
    sugar: 12,
    sodium: 580,
    water: 80,
    calcium: 100,
    iron: 4,
    vitaminC: 15,
    vitaminD: 30,
  },
  'pizza': {
    name: 'Pizza Slice',
    description: 'A slice of pizza with cheese and toppings',
    calories: 320,
    protein: 14,
    carbs: 36,
    fat: 13,
    fiber: 3,
    sugar: 5,
    sodium: 720,
    water: 60,
    calcium: 180,
    iron: 3,
    vitaminC: 10,
    vitaminD: 20,
  },
  // Drinks
  'smoothie': {
    name: 'Fruit Smoothie',
    description: 'A refreshing fruit smoothie',
    calories: 180,
    protein: 6,
    carbs: 35,
    fat: 2,
    fiber: 4,
    sugar: 28,
    sodium: 45,
    water: 250,
    calcium: 200,
    iron: 1,
    vitaminC: 45,
    vitaminD: 60,
  },
  'juice': {
    name: 'Fresh Juice',
    description: 'Freshly squeezed fruit juice',
    calories: 120,
    protein: 2,
    carbs: 28,
    fat: 0,
    fiber: 1,
    sugar: 24,
    sodium: 15,
    water: 220,
    calcium: 30,
    iron: 1,
    vitaminC: 35,
    vitaminD: 25,
  },
  'coffee': {
    name: 'Coffee',
    description: 'A cup of coffee',
    calories: 25,
    protein: 1,
    carbs: 3,
    fat: 1,
    fiber: 0,
    sugar: 0,
    sodium: 5,
    water: 240,
    calcium: 5,
    iron: 0,
    vitaminC: 0,
    vitaminD: 0,
  },
};

// Simulate AI analysis delay
const ANALYSIS_DELAY = 2000; // 2 seconds

/**
 * Analyzes an image and returns nutrition information
 * This is a mock implementation that returns random data
 * In production, this would call an AI service
 */
export async function analyzeNutritionFromImage(
  imageUrl: string,
  logType: 'meal' | 'drink' = 'meal'
): Promise<NutritionAnalysis> {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, ANALYSIS_DELAY));

  // Get random nutrition data based on type
  const foodTypes = Object.keys(MOCK_NUTRITION_DATABASE);
  const relevantTypes = logType === 'drink' 
    ? foodTypes.filter(type => ['smoothie', 'juice', 'coffee'].includes(type))
    : foodTypes.filter(type => !['smoothie', 'juice', 'coffee'].includes(type));
  
  const randomType = relevantTypes[Math.floor(Math.random() * relevantTypes.length)];
  const baseNutrition = MOCK_NUTRITION_DATABASE[randomType as keyof typeof MOCK_NUTRITION_DATABASE];

  // Add some randomization to make it more realistic
  const variation = 0.2; // 20% variation
  const randomize = (value: number) => {
    const min = value * (1 - variation);
    const max = value * (1 + variation);
    return Math.round(Math.random() * (max - min) + min);
  };

  return {
    ...baseNutrition,
    calories: randomize(baseNutrition.calories),
    protein: randomize(baseNutrition.protein),
    carbs: randomize(baseNutrition.carbs),
    fat: randomize(baseNutrition.fat),
    fiber: baseNutrition.fiber ? randomize(baseNutrition.fiber) : undefined,
    sugar: baseNutrition.sugar ? randomize(baseNutrition.sugar) : undefined,
    sodium: baseNutrition.sodium ? randomize(baseNutrition.sodium) : undefined,
    water: baseNutrition.water ? randomize(baseNutrition.water) : undefined,
    calcium: baseNutrition.calcium ? randomize(baseNutrition.calcium) : undefined,
    iron: baseNutrition.iron ? randomize(baseNutrition.iron) : undefined,
    vitaminC: baseNutrition.vitaminC ? randomize(baseNutrition.vitaminC) : undefined,
    vitaminD: baseNutrition.vitaminD ? randomize(baseNutrition.vitaminD) : undefined,
    confidence: Math.random() * 0.3 + 0.7, // 70-100% confidence
    ingredients: generateMockIngredients(randomType),
  };
}

function generateMockIngredients(foodType: string): string[] {
  const ingredientMap: Record<string, string[]> = {
    sandwich: ['bread', 'lettuce', 'tomato', 'cheese', 'meat'],
    salad: ['mixed greens', 'tomatoes', 'cucumber', 'carrots', 'dressing'],
    pasta: ['pasta', 'tomato sauce', 'cheese', 'herbs'],
    pizza: ['dough', 'tomato sauce', 'mozzarella', 'toppings'],
    smoothie: ['banana', 'berries', 'yogurt', 'honey'],
    juice: ['oranges', 'apples', 'natural sugars'],
    coffee: ['coffee beans', 'water'],
  };

  return ingredientMap[foodType] || ['various ingredients'];
}

/**
 * Validates nutrition data
 */
export function validateNutritionData(data: Partial<NutritionAnalysis>): boolean {
  return !!(
    data.name &&
    data.description &&
    typeof data.calories === 'number' &&
    typeof data.protein === 'number' &&
    typeof data.carbs === 'number' &&
    typeof data.fat === 'number'
  );
}

/**
 * Calculates total calories from macronutrients
 */
export function calculateCaloriesFromMacros(protein: number, carbs: number, fat: number): number {
  // 1g protein = 4 calories
  // 1g carbs = 4 calories  
  // 1g fat = 9 calories
  return Math.round(protein * 4 + carbs * 4 + fat * 9);
}

/**
 * Formats nutrition value for display
 */
export function formatNutritionValue(value: number | undefined, unit: string): string {
  if (value === undefined || value === null) return '0' + unit;
  return Math.round(value) + unit;
}
