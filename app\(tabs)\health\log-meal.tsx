import React, { useState } from "react";
import { View, TouchableOpacity, Alert, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useMutation } from "convex/react";
import { X } from "lucide-react-native";

import { api } from "@/convex/_generated/api";
import { useImageUpload } from "@/hooks/useImageUpload";
import {
  analyzeNutritionFromImage,
  type NutritionAnalysis,
} from "@/utils/nutritionAnalysis";
import { Text } from "@/components/ui/text";
import {
  SetupScreen,
  AnalyzingScreen,
  NutritionReviewScreen,
  SuccessScreen,
} from "@/components/log-meal";

type MealType = "breakfast" | "lunch" | "dinner" | "snacks";
type LogType = "meal" | "drink";

// Use the NutritionAnalysis type from the utility
type NutritionData = NutritionAnalysis;

const LogMeal = () => {
  const router = useRouter();
  const { uploadImage } = useImageUpload();
  const createMeal = useMutation(api.meals.createMeal);
  const createDrink = useMutation(api.drinks.createDrink);
  const createMealWithImage = useMutation(api.meals.createMealWithImage);

  // State management
  const [step, setStep] = useState<"setup" | "analyze" | "review" | "success">(
    "setup"
  );
  const [logType, setLogType] = useState<LogType>("meal");
  const [mealType, setMealType] = useState<MealType>("breakfast");
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [nutritionData, setNutritionData] = useState<NutritionData | null>(
    null
  );
  const [isLogging, setIsLogging] = useState(false);

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
  };

  const handleAnalyze = async () => {
    if (!selectedImage) return;

    setStep("analyze");
    setIsAnalyzing(true);
    try {
      // Convert image to blob for upload
      const response = await fetch(selectedImage);
      const blob = await response.blob();

      setIsUploading(true);
      const uploadedImageUrl = await uploadImage(blob, {
        onProgress: (progress) => setUploadProgress(progress.percentage),
      });
      setIsUploading(false);

      // Use the nutrition analysis utility
      const nutritionAnalysis = await analyzeNutritionFromImage(
        uploadedImageUrl,
        logType
      );

      setNutritionData(nutritionAnalysis);
      setStep("review");
    } catch (error) {
      console.error("Error analyzing image:", error);
      setStep("setup"); // Go back to setup on error
    } finally {
      setIsAnalyzing(false);
      setIsUploading(false);
    }
  };

  const handleLogMeal = async () => {
    if (!nutritionData) return;

    setIsLogging(true);
    try {
      const today = new Date().toISOString().split("T")[0];

      if (logType === "meal") {
        await createMeal({
          name: nutritionData.name,
          description: nutritionData.description,
          calories: nutritionData.calories,
          protein: nutritionData.protein,
          carbs: nutritionData.carbs,
          fat: nutritionData.fat,
          fiber: nutritionData.fiber,
          sugar: nutritionData.sugar,
          sodium: nutritionData.sodium,
          water: nutritionData.water,
          calcium: nutritionData.calcium,
          iron: nutritionData.iron,
          vitaminC: nutritionData.vitaminC,
          vitaminD: nutritionData.vitaminD,
          type: mealType,
          date: today,
        });
      } else {
        await createDrink({
          name: nutritionData.name,
          description: nutritionData.description,
          calories: nutritionData.calories,
          protein: nutritionData.protein,
          carbs: nutritionData.carbs,
          fat: nutritionData.fat,
          fiber: nutritionData.fiber,
          sugar: nutritionData.sugar,
          sodium: nutritionData.sodium,
          water: nutritionData.water,
          calcium: nutritionData.calcium,
          iron: nutritionData.iron,
          vitaminC: nutritionData.vitaminC,
          vitaminD: nutritionData.vitaminD,
          type: mealType,
          date: today,
        });
      }

      setStep("success");
    } catch (error) {
      console.error("Error logging meal:", error);
      Alert.alert("Error", "Failed to log meal. Please try again.");
    } finally {
      setIsLogging(false);
    }
  };

  const resetForm = () => {
    setStep("setup");
    setSelectedImage(null);
    setNutritionData(null);
    setUploadProgress(0);
    setIsUploading(false);
    setIsAnalyzing(false);
    setIsLogging(false);
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="flex-row items-center justify-between p-4 border-b border-border">
        <TouchableOpacity onPress={() => router.replace("/(tabs)/health")}>
          <X size={24} className="text-foreground" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold">Log {logType}</Text>
        <View className="w-6" />
      </View>

      <ScrollView>
        {step === "setup" && (
          <SetupScreen
            logType={logType}
            mealType={mealType}
            selectedImage={selectedImage}
            isAnalyzing={isAnalyzing}
            isUploading={isUploading}
            onLogTypeChange={setLogType}
            onMealTypeChange={setMealType}
            onImageSelected={handleImageSelected}
            onAnalyze={handleAnalyze}
          />
        )}

        {step === "analyze" && (
          <AnalyzingScreen
            selectedImage={selectedImage}
            logType={logType}
            isUploading={isUploading}
            uploadProgress={uploadProgress}
            isAnalyzing={isAnalyzing}
          />
        )}

        {step === "review" && nutritionData && (
          <NutritionReviewScreen
            selectedImage={selectedImage}
            nutritionData={nutritionData}
            logType={logType}
            isLogging={isLogging}
            onNutritionDataChange={setNutritionData}
            onBack={() => setStep("setup")}
            onLogMeal={handleLogMeal}
          />
        )}

        {step === "success" && (
          <SuccessScreen
            logType={logType}
            onLogAnother={resetForm}
            onGoBack={() => router.back()}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default LogMeal;
