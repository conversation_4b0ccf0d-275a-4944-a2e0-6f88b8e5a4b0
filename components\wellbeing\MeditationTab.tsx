import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import {
  Brain,
  Plus,
  Play,
  Clock,
  Zap,
  Wind,
  Heart,
  Leaf,
  Mountain,
  Waves,
} from "lucide-react-native";

const quickMeditationTypes = [
  {
    id: "breathing",
    title: "Breathing",
    duration: 5,
    icon: Wind,
    color: "#06b6d4",
  },
  {
    id: "mindfulness",
    title: "Mindfulness",
    duration: 10,
    icon: Leaf,
    color: "#22c55e",
  },
  {
    id: "body-scan",
    title: "Body Scan",
    duration: 15,
    icon: Heart,
    color: "#f59e0b",
  },
  {
    id: "visualization",
    title: "Visualization",
    duration: 20,
    icon: Mountain,
    color: "#8b5cf6",
  },
];

export function MeditationTab() {
  const [selectedMeditation, setSelectedMeditation] = useState<string | null>(
    null
  );

  const meditationSessions = useQuery(api.meditation.getMeditationSessions, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  const totalMeditationTime =
    meditationSessions?.reduce(
      (total, session) => total + session.durationMinutes,
      0
    ) || 0;

  const averageSessionLength = meditationSessions?.length
    ? totalMeditationTime / meditationSessions.length
    : 0;

  const longestStreak = 7; // This would come from a streak calculation

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Quick Start Options */}
      <View className="px-6 mb-6">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Quick Start
        </Text>
        <View className="flex-row flex-wrap">
          {quickMeditationTypes.map((type, index) => {
            const Icon = type.icon;
            return (
              <TouchableOpacity
                key={type.id}
                onPress={() => setSelectedMeditation(type.id)}
                className={`bg-white rounded-2xl p-4 shadow-sm border border-gray-100 ${
                  index % 2 === 0 ? "mr-2" : "ml-2"
                } mb-3`}
                style={{ width: "48%" }}
              >
                <View className="items-center">
                  <View
                    className="w-12 h-12 rounded-xl items-center justify-center mb-3"
                    style={{ backgroundColor: `${type.color}20` }}
                  >
                    <Icon size={24} color={type.color} />
                  </View>
                  <Text className="font-semibold text-gray-900 text-base mb-1">
                    {type.title}
                  </Text>
                  <Text className="text-gray-600 text-sm mb-3">
                    {type.duration} minutes
                  </Text>
                  <View className="bg-gray-900 rounded-xl px-3 py-2 flex-row items-center">
                    <Play size={14} color="#ffffff" />
                    <Text className="text-white font-medium text-sm ml-1">
                      Start
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      {/* Meditation Stats */}
      <View className="px-6 mb-6">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          This Week
        </Text>
        <View className="flex-row mb-3">
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mr-2">
            <View className="items-center">
              <View className="w-10 h-10 rounded-xl bg-blue-100 items-center justify-center mb-2">
                <Clock size={20} color="#3b82f6" />
              </View>
              <Text className="text-xl font-bold text-gray-900">
                {Math.round(totalMeditationTime)}m
              </Text>
              <Text className="text-gray-600 text-sm">Total Time</Text>
            </View>
          </View>
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 ml-2">
            <View className="items-center">
              <View className="w-10 h-10 rounded-xl bg-green-100 items-center justify-center mb-2">
                <Brain size={20} color="#22c55e" />
              </View>
              <Text className="text-xl font-bold text-gray-900">
                {meditationSessions?.length || 0}
              </Text>
              <Text className="text-gray-600 text-sm">Sessions</Text>
            </View>
          </View>
        </View>
        <View className="flex-row">
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mr-2">
            <View className="items-center">
              <View className="w-10 h-10 rounded-xl bg-purple-100 items-center justify-center mb-2">
                <Zap size={20} color="#8b5cf6" />
              </View>
              <Text className="text-xl font-bold text-gray-900">
                {Math.round(averageSessionLength)}m
              </Text>
              <Text className="text-gray-600 text-sm">Avg Session</Text>
            </View>
          </View>
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 ml-2">
            <View className="items-center">
              <View className="w-10 h-10 rounded-xl bg-orange-100 items-center justify-center mb-2">
                <Waves size={20} color="#f59e0b" />
              </View>
              <Text className="text-xl font-bold text-gray-900">
                {longestStreak}
              </Text>
              <Text className="text-gray-600 text-sm">Day Streak</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Recent Sessions */}
      {meditationSessions && meditationSessions.length > 0 && (
        <View className="px-6 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Recent Sessions
          </Text>
          <View className="bg-white rounded-2xl shadow-sm border border-gray-100">
            {meditationSessions.slice(0, 5).map((session, index) => {
              const meditationType = quickMeditationTypes.find(
                (type) => type.id === session.type
              );
              const Icon = meditationType?.icon || Brain;
              const color = meditationType?.color || "#6b7280";

              return (
                <View
                  key={session._id}
                  className={`p-4 ${index > 0 ? "border-t border-gray-100" : ""}`}
                >
                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center flex-1">
                      <View
                        className="w-8 h-8 rounded-lg items-center justify-center mr-3"
                        style={{ backgroundColor: `${color}20` }}
                      >
                        <Icon size={16} color={color} />
                      </View>
                      <View className="flex-1">
                        <Text className="font-medium text-gray-900">
                          {meditationType?.title || session.type}
                        </Text>
                        <Text className="text-gray-600 text-sm">
                          {session.durationMinutes} minutes
                        </Text>
                      </View>
                    </View>
                    <Text className="text-gray-500 text-xs">
                      {new Date(session._creationTime).toLocaleDateString()}
                    </Text>
                  </View>
                  {session.notes && (
                    <Text className="text-gray-600 text-sm mt-2 ml-11">
                      {session.notes}
                    </Text>
                  )}
                </View>
              );
            })}
          </View>
        </View>
      )}

      {/* Empty State */}
      {(!meditationSessions || meditationSessions.length === 0) && (
        <View className="px-6 mb-6">
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 items-center">
            <View className="w-16 h-16 rounded-2xl bg-blue-100 items-center justify-center mb-4">
              <Brain size={32} color="#3b82f6" />
            </View>
            <Text className="font-semibold text-gray-900 text-base mb-2">
              Start Your Meditation Journey
            </Text>
            <Text className="text-gray-600 text-sm text-center mb-4">
              Choose a meditation type above to begin your mindfulness practice
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
}
