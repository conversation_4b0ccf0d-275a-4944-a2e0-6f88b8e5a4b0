import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Wellbeing Goals Mutations
export const createWellbeingGoal = mutation({
  args: {
    type: v.union(
      v.literal('sleep_duration'),
      v.literal('sleep_quality'),
      v.literal('mood_tracking'),
      v.literal('meditation_frequency'),
      v.literal('meditation_duration'),
      v.literal('stress_reduction'),
      v.literal('wellbeing_score'),
    ),
    title: v.string(),
    description: v.string(),
    targetValue: v.number(),
    unit: v.string(),
    startDate: v.string(),
    endDate: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const goalId = await ctx.db.insert('wellbeingGoal', {
      ...args,
      userId: identity.subject,
      currentValue: 0,
      isActive: true,
      achieved: false,
    });

    return goalId;
  },
});

export const updateWellbeingGoal = mutation({
  args: {
    id: v.id('wellbeingGoal'),
    updates: v.object({
      title: v.optional(v.string()),
      description: v.optional(v.string()),
      targetValue: v.optional(v.number()),
      currentValue: v.optional(v.number()),
      unit: v.optional(v.string()),
      endDate: v.optional(v.string()),
      isActive: v.optional(v.boolean()),
      achieved: v.optional(v.boolean()),
      achievedDate: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const goal = await ctx.db.get(args.id);
    if (!goal) {
      throw new Error('Goal not found');
    }

    if (goal.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const deleteWellbeingGoal = mutation({
  args: { id: v.id('wellbeingGoal') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const goal = await ctx.db.get(args.id);
    if (!goal) {
      throw new Error('Goal not found');
    }

    if (goal.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});

export const markGoalAsAchieved = mutation({
  args: { id: v.id('wellbeingGoal') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const goal = await ctx.db.get(args.id);
    if (!goal) {
      throw new Error('Goal not found');
    }

    if (goal.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, {
      achieved: true,
      achievedDate: new Date().toISOString().split('T')[0],
    });

    return { success: true };
  },
});

// Wellbeing Goals Queries
export const getWellbeingGoals = query({
  args: {
    isActive: v.optional(v.boolean()),
    type: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    let query = ctx.db
      .query('wellbeingGoal')
      .filter((q) => q.eq(q.field('userId'), identity.subject));

    if (args.isActive !== undefined) {
      query = query.filter((q) => q.eq(q.field('isActive'), args.isActive));
    }

    if (args.type) {
      query = query.filter((q) => q.eq(q.field('type'), args.type));
    }

    return await query.order('desc').collect();
  },
});

export const getActiveWellbeingGoals = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return await ctx.db
      .query('wellbeingGoal')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.eq(q.field('isActive'), true))
      .filter((q) => q.eq(q.field('achieved'), false))
      .collect();
  },
});

export const getGoalProgress = query({
  args: { id: v.id('wellbeingGoal') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const goal = await ctx.db.get(args.id);
    if (!goal) {
      throw new Error('Goal not found');
    }

    if (goal.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    const progressPercentage = goal.targetValue > 0 
      ? Math.min(100, ((goal.currentValue || 0) / goal.targetValue) * 100)
      : 0;

    const today = new Date();
    const startDate = new Date(goal.startDate);
    const endDate = new Date(goal.endDate);
    
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysPassed = Math.ceil((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysRemaining = Math.max(0, totalDays - daysPassed);

    return {
      goal,
      progressPercentage: Math.round(progressPercentage),
      daysRemaining,
      totalDays,
      daysPassed: Math.max(0, daysPassed),
      isOverdue: today > endDate && !goal.achieved,
    };
  },
});

export const updateGoalProgress = mutation({
  args: {
    goalType: v.union(
      v.literal('sleep_duration'),
      v.literal('sleep_quality'),
      v.literal('mood_tracking'),
      v.literal('meditation_frequency'),
      v.literal('meditation_duration'),
      v.literal('stress_reduction'),
      v.literal('wellbeing_score'),
    ),
    value: v.number(),
    operation: v.union(v.literal('set'), v.literal('increment')),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Find active goals of this type
    const activeGoals = await ctx.db
      .query('wellbeingGoal')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.eq(q.field('type'), args.goalType))
      .filter((q) => q.eq(q.field('isActive'), true))
      .filter((q) => q.eq(q.field('achieved'), false))
      .collect();

    for (const goal of activeGoals) {
      let newValue: number;
      
      if (args.operation === 'set') {
        newValue = args.value;
      } else {
        newValue = (goal.currentValue || 0) + args.value;
      }

      const achieved = newValue >= goal.targetValue;

      await ctx.db.patch(goal._id, {
        currentValue: newValue,
        achieved,
        achievedDate: achieved ? new Date().toISOString().split('T')[0] : undefined,
      });
    }

    return { success: true };
  },
});
