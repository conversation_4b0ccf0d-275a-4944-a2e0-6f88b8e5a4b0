import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from "react-native";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Moon, Plus, Clock, Star, TrendingUp } from "lucide-react-native";
import { BarChart } from "react-native-gifted-charts";

const { width } = Dimensions.get("window");

export function SleepTab() {
  const [showSleepForm, setShowSleepForm] = useState(false);

  const sleepSummary = useQuery(api.sleep.getLast7DaysSleepSummary);
  const lastSleepEntry = useQuery(api.sleep.getLastSleepEntry);
  const averageSleepDuration = useQuery(api.sleep.getAverageSleepDuration, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  const formatTime = (date: string) => {
    return new Date(date).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDuration = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.round((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Quick Log Button */}
      <View className="px-6 mb-6">
        <TouchableOpacity
          onPress={() => setShowSleepForm(true)}
          className="bg-purple-600 rounded-2xl p-4 flex-row items-center justify-center"
        >
          <Plus size={20} color="#ffffff" />
          <Text className="text-white font-semibold text-base ml-2">
            Log Sleep
          </Text>
        </TouchableOpacity>
      </View>

      {/* Sleep Metrics */}
      <View className="px-6 mb-6">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Sleep Metrics
        </Text>
        <View className="flex-row mb-3">
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mr-2">
            <View className="items-center">
              <View className="w-10 h-10 rounded-xl bg-purple-100 items-center justify-center mb-2">
                <Moon size={20} color="#8b5cf6" />
              </View>
              <Text className="text-xl font-bold text-gray-900">
                {lastSleepEntry?.qualityRating || 0}/5
              </Text>
              <Text className="text-gray-600 text-sm">Last Quality</Text>
            </View>
          </View>
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 ml-2">
            <View className="items-center">
              <View className="w-10 h-10 rounded-xl bg-blue-100 items-center justify-center mb-2">
                <Clock size={20} color="#3b82f6" />
              </View>
              <Text className="text-xl font-bold text-gray-900">
                {averageSleepDuration
                  ? formatDuration(averageSleepDuration)
                  : "0h"}
              </Text>
              <Text className="text-gray-600 text-sm">Avg Duration</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Last Sleep Entry */}
      {lastSleepEntry && (
        <View className="px-6 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Last Sleep
          </Text>
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <View className="flex-row items-center justify-between mb-3">
              <Text className="font-semibold text-gray-900">
                {new Date(lastSleepEntry.date).toLocaleDateString()}
              </Text>
              <View className="flex-row items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    size={16}
                    color={
                      i < lastSleepEntry.qualityRating ? "#fbbf24" : "#e5e7eb"
                    }
                    fill={
                      i < lastSleepEntry.qualityRating
                        ? "#fbbf24"
                        : "transparent"
                    }
                  />
                ))}
              </View>
            </View>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Bedtime</Text>
              <Text className="font-medium text-gray-900">
                {formatTime(lastSleepEntry.startTime)}
              </Text>
            </View>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-gray-600">Wake Time</Text>
              <Text className="font-medium text-gray-900">
                {formatTime(lastSleepEntry.endTime)}
              </Text>
            </View>
            <View className="flex-row justify-between items-center">
              <Text className="text-gray-600">Duration</Text>
              <Text className="font-medium text-gray-900">
                {formatDuration(lastSleepEntry.durationHours)}
              </Text>
            </View>
            {lastSleepEntry.notes && (
              <View className="mt-3 pt-3 border-t border-gray-100">
                <Text className="text-gray-600 text-sm">
                  {lastSleepEntry.notes}
                </Text>
              </View>
            )}
          </View>
        </View>
      )}

      {/* Sleep Pattern Chart */}
      {sleepSummary && sleepSummary.length > 0 && (
        <View className="px-6 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            7-Day Sleep Pattern
          </Text>
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <BarChart
              data={sleepSummary.map((item) => ({
                value: item.value,
                label: item.label,
                frontColor: "#8b5cf6",
              }))}
              width={width - 80}
              height={180}
              barWidth={32}
              spacing={20}
              roundedTop
              roundedBottom
              hideRules
              xAxisThickness={0}
              yAxisThickness={0}
              yAxisTextStyle={{ color: "#9ca3af", fontSize: 12 }}
              xAxisLabelTextStyle={{ color: "#9ca3af", fontSize: 12 }}
              noOfSections={4}
              maxValue={10}
            />
          </View>
        </View>
      )}
    </ScrollView>
  );
}
