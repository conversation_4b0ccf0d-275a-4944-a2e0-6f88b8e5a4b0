import React, { useState } from "react";
import { TouchableOpacity, ScrollView, View } from "react-native";
import {
  <PERSON>,
  <PERSON>,
  Baby,
  <PERSON><PERSON>bell,
  Stethoscope,
  UserCog,
  Wheat,
  Activity,
  Syringe, // For Diabetes
  Leaf, // For Vegan
  Bone, // For Muscle Gain
  Soup, // For Gut Health
  Sparkles, // For Skin Enhancement
  Droplet,
  Weight,
  ForkKnife,
  ForkKnifeCrossed, // For Detox & Cleanse
  Target,
  TrendingUp,
  Shield,
  CheckCircle,
} from "lucide-react-native";
import { Doc } from "@/convex/_generated/dataModel";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { CreateHealthPlanForm } from "./CreateHealthPlanForm";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "../ui/dialog";
import { Text } from "../ui/text";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "../ui/card";
import { iconWithClassName } from "@/lib/icons/iconWithClassName";
import { healthPlansData, HealthPlanType } from "@/data/healthPlansData";
import { router } from "expo-router";

type HealthPlan = Doc<"healthPlan">;

// Apply cssInterop to each icon to enable className prop
iconWithClassName(Scale);
iconWithClassName(Heart);
iconWithClassName(Baby);
iconWithClassName(Dumbbell);
iconWithClassName(Stethoscope);
iconWithClassName(UserCog);
iconWithClassName(Wheat);
iconWithClassName(Activity);
iconWithClassName(Syringe);
iconWithClassName(Leaf);
iconWithClassName(Bone);
iconWithClassName(Soup);
iconWithClassName(Sparkles);
iconWithClassName(Droplet);
iconWithClassName(Target);
iconWithClassName(TrendingUp);
iconWithClassName(Shield);
iconWithClassName(CheckCircle);

const getPlanDetails = (type: HealthPlanType) => {
  const plan = healthPlansData.find((p) => p.type === type);
  if (plan) {
    let iconComponent;
    switch (type) {
      case "weight":
        iconComponent = <Scale size={24} className="text-primary" />;
        break;
      case "heart":
        iconComponent = <Heart size={24} className="text-primary" />;
        break;
      case "prenatal":
        iconComponent = <Baby size={24} className="text-primary" />;
        break;
      case "sports":
        iconComponent = <Dumbbell size={24} className="text-primary" />;
        break;
      case "recovery":
        iconComponent = <Stethoscope size={24} className="text-primary" />;
        break;
      case "senior":
        iconComponent = <UserCog size={24} className="text-primary" />;
        break;
      case "gluten":
        iconComponent = <Wheat size={24} className="text-primary" />;
        break;
      case "diabetes":
        iconComponent = <Syringe size={24} className="text-primary" />;
        break;
      case "gut":
        iconComponent = <Soup size={24} className="text-primary" />;
        break;
      case "vegan":
        iconComponent = <Leaf size={24} className="text-primary" />;
        break;
      case "muscle":
        iconComponent = <Bone size={24} className="text-primary" />;
        break;
      case "skin":
        iconComponent = <Sparkles size={24} className="text-primary" />;
        break;
      case "detox":
        iconComponent = <Droplet size={24} className="text-primary" />;
        break;
      default:
        iconComponent = null;
    }
    return {
      title: plan.title,
      description: plan.description,
      icon: iconComponent,
    };
  }
  return {
    title: "Unknown Plan",
    description: "No description available for this plan type.",
    icon: null,
  };
};

export function HealthPlanCard({
  activePlan,
}: {
  activePlan: HealthPlan | undefined;
}) {
  const userProfile = useQuery(api.userProfile.getProfile);
  const today = new Date();
  const todayISOString = today.toISOString().split("T")[0];

  const mealSummary = useQuery(
    api.meals.getMealsSummary,
    activePlan
      ? {
          startDate: activePlan.startDate.split("T")[0],
          endDate: todayISOString,
        }
      : {
          startDate: todayISOString,
          endDate: todayISOString,
        }
  );

  const planDetails = activePlan ? getPlanDetails(activePlan.type) : null;

  const daysActive = activePlan
    ? Math.max(
        1,
        Math.ceil(
          (today.getTime() - new Date(activePlan._creationTime).getTime()) /
            (1000 * 60 * 60 * 24)
        )
      )
    : 0;

  const totalDailyCalorieGoal = activePlan
    ? activePlan.dailyCalories.breakfast +
      activePlan.dailyCalories.lunch +
      activePlan.dailyCalories.dinner +
      activePlan.dailyCalories.snacks
    : 0;

  const totalExpectedCalories = daysActive * totalDailyCalorieGoal;

  const consumedCalories = mealSummary?.totalCalories ?? 0;

  const planAdherence =
    totalExpectedCalories > 0
      ? Math.round((consumedCalories / totalExpectedCalories) * 100)
      : 0;

  return (
    <View className="mx-4 mt-4">
      {activePlan && planDetails ? (
        <Card className="rounded-lg p-4 shadow-md bg-card">
          <CardContent className="p-0">
            <View className="flex-row items-center mb-4">
              <View className="w-12 h-12 rounded-full bg-secondary justify-center items-center shadow-sm">
                {planDetails.icon}
              </View>
              <View className="flex-1 ml-4">
                <Text className="font-bold text-lg text-foreground mb-1">
                  {planDetails.title}
                </Text>
                <Text className="font-normal text-sm text-muted-foreground">
                  {planDetails.description}
                </Text>
              </View>
            </View>
            <View className="flex-row justify-around pt-4 border-t border-border mb-4">
              <View className="items-center">
                <Activity size={16} color="green" />
                <Text className="font-bold text-lg text-foreground my-1">
                  {planAdherence}%
                </Text>
                <Text className="font-normal text-xs text-muted-foreground">
                  Plan Adherence
                </Text>
              </View>
              <View className="w-px h-full bg-border" />
              <View className="items-center">
                <Heart size={16} color={"indigo"} />
                <Text className="font-bold text-lg text-foreground my-1">
                  {daysActive}
                </Text>
                <Text className="font-normal text-xs text-muted-foreground">
                  Days Active
                </Text>
              </View>
              <View className="w-px h-full bg-border" />
              <View className="items-center">
                <Weight size={16} color="orange" className="text-primary" />
                <Text className="font-bold text-lg text-foreground my-1">
                  {userProfile === undefined
                    ? "..."
                    : userProfile?.weight || "N/A"}
                </Text>
                <Text className="font-normal text-xs text-muted-foreground">
                  Body weight
                </Text>
              </View>
            </View>

            <View className="mt-3 bg-white gap-4 flex-row w-full">
              <Button
                style={{
                  borderColor: "indigo",
                  borderWidth: 1,
                }}
                className="flex-1 "
                variant={"ghost"}
                onPress={() => router.push("/health/edit-plan")}
              >
                <Text>Change Plan</Text>
              </Button>
              <Button
                onPress={() => router.push("/health/log-meal")}
                className="flex-1 flex-row gap-4"
              >
                <ForkKnifeCrossed color={"white"} size={16} />
                <Text>Log Meal</Text>
              </Button>
            </View>
          </CardContent>
        </Card>
      ) : (
        <Card className="rounded-lg p-6 shadow-md bg-card">
          <CardContent className="p-0">
            {/* Header with Icon */}
            <View className="items-center mb-6">
              <View className="w-16 h-16 rounded-full bg-primary/10 justify-center items-center mb-4">
                <Target size={32} className="text-primary" />
              </View>
              <Text className="font-bold text-xl text-foreground text-center mb-2">
                Start Your Health Journey
              </Text>
              <Text className="text-sm text-muted-foreground text-center leading-5">
                Create a personalized health plan to track your nutrition goals
                and build healthier eating habits
              </Text>
            </View>

            {/* Benefits List */}
            <View className="mb-6 space-y-3">
              <View className="flex-row items-center">
                <View className="w-8 h-8 rounded-full bg-green-100 justify-center items-center mr-3">
                  <CheckCircle size={16} className="text-green-600" />
                </View>
                <Text className="flex-1 text-sm text-foreground">
                  Track daily nutrition and calorie intake
                </Text>
              </View>

              <View className="flex-row items-center">
                <View className="w-8 h-8 rounded-full bg-blue-100 justify-center items-center mr-3">
                  <TrendingUp size={16} className="text-blue-600" />
                </View>
                <Text className="flex-1 text-sm text-foreground">
                  Monitor progress towards your health goals
                </Text>
              </View>

              <View className="flex-row items-center">
                <View className="w-8 h-8 rounded-full bg-purple-100 justify-center items-center mr-3">
                  <Shield size={16} className="text-purple-600" />
                </View>
                <Text className="flex-1 text-sm text-foreground">
                  Get personalized recommendations for your lifestyle
                </Text>
              </View>
            </View>

            {/* Call to Action */}
            <Button
              onPress={() => router.push("/health/new-plan")}
              className="w-full flex-row gap-3"
            >
              <Target size={16} className="text-white mr-2" />
              <Text className="text-white font-medium">
                Create Your Health Plan
              </Text>
            </Button>

            <Text className="text-xs text-muted-foreground text-center mt-3">
              Takes less than 20 seconds to set up
            </Text>
          </CardContent>
        </Card>
      )}
    </View>
  );
}
