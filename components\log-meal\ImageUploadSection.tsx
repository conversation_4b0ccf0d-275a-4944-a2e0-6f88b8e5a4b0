import React from "react";
import { View, Image, Alert } from "react-native";
import { Camera, Upload } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";

interface ImageUploadSectionProps {
  selectedImage: string | null;
  onImageSelected: (uri: string) => void;
  isAnalyzing?: boolean;
  isUploading?: boolean;
  onAnalyze?: () => void;
}

export const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  selectedImage,
  onImageSelected,
  isAnalyzing = false,
  isUploading = false,
  onAnalyze,
}) => {
  const handleImageSelection = async (source: "camera" | "gallery") => {
    try {
      let result;

      if (source === "camera") {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Camera permission is required to take photos"
          );
          return;
        }
        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      } else {
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets[0]) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error selecting image:", error);
      Alert.alert("Error", "Failed to select image. Please try again.");
    }
  };

  return (
    <Card className="mb-6 border-0">
      {selectedImage ? (
        <View>
          <Image
            source={{ uri: selectedImage }}
            className="w-full h-48 rounded-lg mb-4"
            resizeMode="cover"
          />
          <View className="flex-row gap-3 mb-4">
            <Button
              onPress={() => handleImageSelection("camera")}
              variant="outline"
              size="lg"
              className="flex-1 flex-row gap-3"
            >
              <Camera size={16} className="mr-2" />
              <Text>Retake</Text>
            </Button>
            <Button
              onPress={() => handleImageSelection("gallery")}
              variant="outline"
              size="lg"
              className="flex-1 flex-row gap-3"
            >
              <Upload size={16} className="mr-2" />
              <Text>Choose</Text>
            </Button>
          </View>
          {onAnalyze && (
            <Button
              onPress={onAnalyze}
              size="lg"
              className="w-full"
              disabled={isAnalyzing || isUploading}
            >
              <Text className="text-white font-semibold">
                {isAnalyzing || isUploading
                  ? "Analyzing..."
                  : "Analyze & Continue"}
              </Text>
            </Button>
          )}
        </View>
      ) : (
        <View>
          <View className="items-center mb-6">
            <Text className="text-muted-foreground text-center">
              Take a photo or select an image for AI nutrition analysis
            </Text>
          </View>
          <View className="flex-row gap-3">
            <Button
              onPress={() => handleImageSelection("camera")}
              variant="outline"
              size="lg"
              className="flex-1 flex-row gap-3"
            >
              <Camera size={18} className="mr-2" />
              <Text className="text-sm">Take Photo</Text>
            </Button>
            <Button
              onPress={() => handleImageSelection("gallery")}
              variant="outline"
              size="lg"
              className="flex-1 flex-row gap-3"
            >
              <Upload size={18} className="mr-2" />
              <Text className="text-sm">Gallery</Text>
            </Button>
          </View>
        </View>
      )}
    </Card>
  );
};
