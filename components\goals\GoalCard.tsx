import React from "react";
import { View, Text, TouchableOpacity, Alert } from "react-native";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import {
  Award,
  Edit3,
  Trash2,
  CheckCircle,
  Circle,
  Pause,
  Play,
  MoreVertical,
  Calendar,
  Target,
} from "lucide-react-native";
import { getGoalRule, WellbeingGoalType } from "@/data/wellbeingGoalsData";

interface Goal {
  _id: Id<"wellbeingGoal">;
  type: string;
  title: string;
  description: string;
  targetValue: number;
  currentValue?: number;
  unit: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  achieved?: boolean;
}

interface GoalCardProps {
  goal: Goal;
  onEdit: (goal: Goal) => void;
  onRefresh: () => void;
}

export function GoalCard({ goal, onEdit, onRefresh }: GoalCardProps) {
  const updateGoal = useMutation(api.wellbeingGoals.updateWellbeingGoal);
  const deleteGoal = useMutation(api.wellbeingGoals.deleteWellbeingGoal);
  const markAsAchieved = useMutation(api.wellbeingGoals.markGoalAsAchieved);

  // Get goal rule for enhanced display
  const goalRule = getGoalRule(goal.type as WellbeingGoalType);

  const progressPercentage = Math.round(
    ((goal.currentValue || 0) / goal.targetValue) * 100
  );
  const isCompleted = goal.achieved;

  // Calculate days remaining
  const today = new Date();
  const endDate = new Date(goal.endDate);
  const daysRemaining = Math.max(
    0,
    Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
  );

  const handleToggleActive = async () => {
    try {
      await updateGoal({
        id: goal._id,
        updates: { isActive: !goal.isActive },
      });
      onRefresh();
    } catch (error) {
      Alert.alert("Error", "Failed to update goal status");
    }
  };

  const handleMarkComplete = async () => {
    try {
      if (goal.achieved) {
        await updateGoal({
          id: goal._id,
          updates: { achieved: false, achievedDate: undefined },
        });
      } else {
        await markAsAchieved({ id: goal._id });
      }
      onRefresh();
    } catch (error) {
      Alert.alert("Error", "Failed to update goal completion");
    }
  };

  const handleDelete = () => {
    Alert.alert(
      "Delete Goal",
      "Are you sure you want to delete this goal? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await deleteGoal({ id: goal._id });
              onRefresh();
            } catch (error) {
              Alert.alert("Error", "Failed to delete goal");
            }
          },
        },
      ]
    );
  };

  return (
    <View
      className={`bg-white rounded-2xl p-4 mb-4 border ${
        isCompleted
          ? "border-green-200"
          : goal.isActive
            ? "border-gray-100"
            : "border-gray-200"
      } shadow-sm`}
    >
      {/* Header with title and actions */}
      <View className="flex-row items-start justify-between mb-3">
        <View className="flex-1 mr-3">
          <View className="flex-row items-center mb-2">
            {goalRule && <Text className="text-2xl mr-3">{goalRule.icon}</Text>}
            <View className="flex-1">
              <Text
                className={`text-lg font-semibold ${
                  isCompleted ? "text-green-800" : "text-gray-900"
                }`}
              >
                {goal.title}
              </Text>
              {goalRule && (
                <View className="flex-row items-center mt-1">
                  <View className="bg-gray-100 rounded-full px-2 py-1">
                    <Text className="text-xs font-medium text-gray-600 capitalize">
                      {goalRule.category}
                    </Text>
                  </View>
                  <Text className="text-xs text-gray-500 ml-2">
                    {goalRule.trackingFrequency}
                  </Text>
                </View>
              )}
            </View>
            {isCompleted && (
              <View className="ml-2 bg-green-50 rounded-full p-1">
                <Award size={16} color="#8BC34A" />
              </View>
            )}
            {!goal.isActive && !isCompleted && (
              <View className="ml-2 bg-gray-50 rounded-full p-1">
                <Pause size={16} color="#808080" />
              </View>
            )}
          </View>
          <Text
            className={`text-sm ${
              isCompleted ? "text-green-600" : "text-gray-600"
            }`}
          >
            {goal.description}
          </Text>
        </View>

        {/* Action buttons */}
        <View className="flex-row space-x-1">
          <TouchableOpacity
            onPress={handleMarkComplete}
            className="p-2 rounded-lg bg-gray-50"
          >
            {isCompleted ? (
              <CheckCircle size={18} color="#8BC34A" />
            ) : (
              <Circle size={18} color="#808080" />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => onEdit(goal)}
            className="p-2 rounded-lg bg-gray-50"
          >
            <Edit3 size={18} color="#606060" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleToggleActive}
            className="p-2 rounded-lg bg-gray-50"
          >
            {goal.isActive ? (
              <Pause size={18} color="#606060" />
            ) : (
              <Play size={18} color="#606060" />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleDelete}
            className="p-2 rounded-lg bg-gray-50"
          >
            <Trash2 size={18} color="#EF5350" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Progress section */}
      {!isCompleted && (
        <View className="mb-3">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-sm text-gray-600">Progress</Text>
            <Text className="text-sm font-semibold text-gray-900">
              {progressPercentage}%
            </Text>
          </View>
          <View className="bg-gray-100 rounded-full h-2">
            <View
              className={`h-2 rounded-full ${
                goal.isActive ? "bg-gray-900" : "bg-gray-400"
              }`}
              style={{ width: `${Math.min(100, progressPercentage)}%` }}
            />
          </View>
        </View>
      )}

      {/* Footer with current progress and end date */}
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center">
          <Target size={14} color={isCompleted ? "#22c55e" : "#6B7280"} />
          <Text
            className={`text-sm ml-1 font-medium ${
              isCompleted ? "text-green-600" : "text-gray-700"
            }`}
          >
            {goal.currentValue || 0} / {goal.targetValue} {goal.unit}
          </Text>
        </View>
        <View className="flex-row items-center">
          <Calendar size={14} color={isCompleted ? "#22c55e" : "#6B7280"} />
          <Text
            className={`text-sm ml-1 ${
              isCompleted ? "text-green-500" : "text-gray-500"
            }`}
          >
            {isCompleted
              ? "Completed"
              : daysRemaining === 0
                ? "Due today"
                : daysRemaining === 1
                  ? "1 day left"
                  : `${daysRemaining} days left`}
          </Text>
        </View>
      </View>

      {/* Status indicator */}
      {!goal.isActive && !isCompleted && (
        <View className="mt-2 pt-2 border-t border-gray-200">
          <Text className="text-xs text-gray-500 text-center">
            Goal is paused - tap play to resume
          </Text>
        </View>
      )}
    </View>
  );
}
