import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import {
  ChevronLeft,
  Heart,
  Brain,
  Zap,
  Shield,
} from 'lucide-react-native';

export default function WellbeingCheck() {
  const router = useRouter();
  const [stress, setStress] = useState<number | null>(null);
  const [anxiety, setAnxiety] = useState<number | null>(null);
  const [happiness, setHappiness] = useState<number | null>(null);
  const [energy, setEnergy] = useState<number | null>(null);
  const [notes, setNotes] = useState('');

  const createWellbeingEntry = useMutation(api.wellbeingEntry.createWellbeingEntry);
  const currentEntry = useQuery(api.wellbeingEntry.getCurrentWellbeingEntry);

  React.useEffect(() => {
    if (currentEntry) {
      setStress(currentEntry.stress || null);
      setAnxiety(currentEntry.anxiety || null);
      setHappiness(currentEntry.happiness || null);
      setEnergy(currentEntry.energy || null);
      setNotes(currentEntry.notes || '');
    }
  }, [currentEntry]);

  const handleSave = async () => {
    if (!stress && !anxiety && !happiness && !energy) {
      Alert.alert('Error', 'Please rate at least one aspect of your wellbeing');
      return;
    }

    try {
      await createWellbeingEntry({
        date: new Date().toISOString().split('T')[0],
        stress: stress || undefined,
        anxiety: anxiety || undefined,
        happiness: happiness || undefined,
        energy: energy || undefined,
        notes: notes || undefined,
      });

      Alert.alert('Success', 'Wellbeing check saved successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to save wellbeing check');
    }
  };

  const RatingScale = ({
    title,
    value,
    onValueChange,
    icon: Icon,
    color,
    lowLabel,
    highLabel,
  }: {
    title: string;
    value: number | null;
    onValueChange: (value: number) => void;
    icon: any;
    color: string;
    lowLabel: string;
    highLabel: string;
  }) => (
    <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
      <View className="flex-row items-center mb-4">
        <View
          className="w-10 h-10 rounded-xl items-center justify-center mr-3"
          style={{ backgroundColor: `${color}20` }}
        >
          <Icon size={20} color={color} />
        </View>
        <Text className="text-lg font-semibold text-gray-900">{title}</Text>
      </View>

      <View className="items-center mb-4">
        <Text className="text-3xl font-bold mb-2" style={{ color }}>
          {value || '-'}/10
        </Text>
        <View className="flex-row items-center justify-between w-full mb-4">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((level) => (
            <TouchableOpacity
              key={level}
              onPress={() => onValueChange(level)}
              className={`w-8 h-8 rounded-full items-center justify-center ${
                level <= (value || 0) ? 'opacity-100' : 'opacity-30'
              }`}
              style={{ backgroundColor: color }}
            >
              <Text className="text-white text-xs font-bold">{level}</Text>
            </TouchableOpacity>
          ))}
        </View>
        <View className="flex-row justify-between w-full">
          <Text className="text-gray-500 text-sm">{lowLabel}</Text>
          <Text className="text-gray-500 text-sm">{highLabel}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-row items-center px-6 py-4 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <ChevronLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900">Wellbeing Check</Text>
      </View>

      <ScrollView className="flex-1 px-6 py-6">
        <Text className="text-center text-gray-600 mb-6">
          Take a moment to check in with yourself. Rate how you're feeling today on a scale of 1-10.
        </Text>

        <RatingScale
          title="Stress Level"
          value={stress}
          onValueChange={setStress}
          icon={Brain}
          color="#ef4444"
          lowLabel="Very Calm"
          highLabel="Very Stressed"
        />

        <RatingScale
          title="Anxiety Level"
          value={anxiety}
          onValueChange={setAnxiety}
          icon={Shield}
          color="#f97316"
          lowLabel="Very Relaxed"
          highLabel="Very Anxious"
        />

        <RatingScale
          title="Happiness Level"
          value={happiness}
          onValueChange={setHappiness}
          icon={Heart}
          color="#22c55e"
          lowLabel="Very Sad"
          highLabel="Very Happy"
        />

        <RatingScale
          title="Energy Level"
          value={energy}
          onValueChange={setEnergy}
          icon={Zap}
          color="#3b82f6"
          lowLabel="Very Tired"
          highLabel="Very Energetic"
        />

        {/* Notes */}
        <View className="bg-white rounded-2xl p-4 mb-6 shadow-sm border border-gray-100">
          <Text className="text-lg font-semibold text-gray-900 mb-3">
            Additional Notes (Optional)
          </Text>
          <TextInput
            value={notes}
            onChangeText={setNotes}
            placeholder="How are you feeling today? Any specific thoughts or observations?"
            multiline
            numberOfLines={4}
            className="bg-gray-50 rounded-xl p-3 text-gray-900"
            textAlignVertical="top"
          />
        </View>

        {/* Save Button */}
        <TouchableOpacity
          onPress={handleSave}
          className="bg-blue-500 rounded-2xl p-4 items-center mb-8"
        >
          <Text className="text-white font-semibold text-lg">Save Wellbeing Check</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}
