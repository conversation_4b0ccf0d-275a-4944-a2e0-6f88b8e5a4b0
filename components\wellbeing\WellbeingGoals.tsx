import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Target, Plus, Edit3, Calendar, TrendingUp } from "lucide-react-native";
import { useRouter } from "expo-router";
import { getGoalRule, WellbeingGoalType } from "@/data/wellbeingGoalsData";

export function WellbeingGoals() {
  const router = useRouter();
  const activeGoals = useQuery(api.wellbeingGoals.getActiveWellbeingGoals);
  const currentGoal = activeGoals?.[0]; // Get the primary active goal

  // Get goal rule for enhanced display
  const goalRule = currentGoal
    ? getGoalRule(currentGoal.type as WellbeingGoalType)
    : null;

  const handleEditGoal = (goal: any) => {
    // For now, we'll navigate to the create screen, but you might want a dedicated edit screen
    router.push("/(tabs)/wellbeing/create-goal");
  };

  const handleCreateGoal = () => {
    router.push("/(tabs)/wellbeing/create-goal");
  };

  const handleViewAllGoals = () => {
    router.push("/(tabs)/wellbeing/goals");
  };

  // Calculate days remaining for current goal
  const daysRemaining = currentGoal
    ? Math.max(
        0,
        Math.ceil(
          (new Date(currentGoal.endDate).getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        )
      )
    : 0;

  return (
    <View className="mx-6 mb-6">
      <View className="flex-row items-center justify-between mb-4">
        <View>
          <Text className="text-xl font-semibold text-gray-900">
            Current Goal
          </Text>
        </View>
        <View className="flex-row items-center">
          <TouchableOpacity onPress={handleViewAllGoals} className="mr-2">
            <Text className="text-sm font-medium text-gray-600">View All</Text>
          </TouchableOpacity>
        </View>
      </View>

      {currentGoal ? (
        <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center">
              <View className="w-12 h-12 rounded-xl bg-gray-100 items-center justify-center mr-3">
                {goalRule ? (
                  <Text className="text-2xl">{goalRule.icon}</Text>
                ) : (
                  <Target size={20} color="#6b7280" />
                )}
              </View>
              <View className="flex-1">
                <Text className="font-semibold text-gray-900 text-base">
                  {currentGoal.title}
                </Text>
                <View className="flex-row items-center mt-1">
                  {goalRule && (
                    <View className="bg-gray-100 rounded-full px-2 py-1 mr-2">
                      <Text className="text-xs font-medium text-gray-600 capitalize">
                        {goalRule.category}
                      </Text>
                    </View>
                  )}
                  <View className="flex-row items-center">
                    <Calendar size={12} color="#6b7280" />
                    <Text className="text-xs text-gray-500 ml-1">
                      {daysRemaining === 0
                        ? "Due today"
                        : daysRemaining === 1
                          ? "1 day left"
                          : `${daysRemaining} days left`}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
            <TouchableOpacity
              onPress={() => handleEditGoal(currentGoal)}
              className="p-2"
            >
              <Edit3 size={16} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <View className="mb-2">
            <View className="flex-row items-center justify-between mb-1">
              <View className="flex-row items-center">
                <Text className="text-sm text-gray-600">Progress</Text>
                <TrendingUp size={14} color="#22c55e" className="ml-1" />
              </View>
              <Text className="text-sm font-medium text-gray-900">
                {Math.round(
                  ((currentGoal.currentValue || 0) / currentGoal.targetValue) *
                    100
                )}
                %
              </Text>
            </View>
            <View className="bg-gray-200 rounded-full h-2">
              <View
                className="bg-gray-900 h-2 rounded-full"
                style={{
                  width: `${Math.min(100, ((currentGoal.currentValue || 0) / currentGoal.targetValue) * 100)}%`,
                }}
              />
            </View>
          </View>

          <View className="flex-row items-center justify-between">
            <Text className="text-xs text-gray-600">
              {currentGoal.currentValue || 0} / {currentGoal.targetValue}{" "}
              {currentGoal.unit}
            </Text>
            {goalRule && (
              <Text className="text-xs text-gray-500">
                {goalRule.trackingFrequency}
              </Text>
            )}
          </View>
        </View>
      ) : (
        <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 items-center">
          <View className="w-12 h-12 rounded-xl bg-gray-100 items-center justify-center mb-3">
            <Target size={24} color="#6b7280" />
          </View>
          <Text className="font-semibold text-gray-900 text-base mb-2">
            No Active Goal
          </Text>
          <Text className="text-gray-600 text-sm text-center mb-4">
            Set a wellbeing goal to track your progress and stay motivated
          </Text>
          <TouchableOpacity
            onPress={handleCreateGoal}
            className="bg-gray-900 rounded-xl px-4 py-2 flex-row items-center"
          >
            <Plus size={16} color="#ffffff" />
            <Text className="text-white font-medium ml-2">Create Goal</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
