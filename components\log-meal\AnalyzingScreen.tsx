import React from "react";
import { View, Image } from "react-native";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Progress } from "@/components/ui/progress";

interface AnalyzingScreenProps {
  selectedImage: string | null;
  logType: "meal" | "drink";
  isUploading: boolean;
  uploadProgress: number;
  isAnalyzing: boolean;
}

export const AnalyzingScreen: React.FC<AnalyzingScreenProps> = ({
  selectedImage,
  logType,
  isUploading,
  uploadProgress,
  isAnalyzing,
}) => {
  return (
    <View className="flex-1 p-6">
      <Text className="text-2xl font-bold mb-2">Analyzing Image</Text>
      <Text className="text-muted-foreground mb-8">
        Please wait while our AI analyzes your {logType} image...
      </Text>

      {selectedImage && (
        <Card className="p-4 mb-6">
          <Image
            source={{ uri: selectedImage }}
            className="w-full h-48 rounded-lg mb-4"
            resizeMode="cover"
          />
        </Card>
      )}

      {isUploading && (
        <Card className="p-6 mb-6">
          <Text className="text-lg font-semibold mb-4">Uploading Image</Text>
          <Progress value={uploadProgress} className="mb-2" />
          <Text className="text-sm text-muted-foreground">
            {uploadProgress}% complete
          </Text>
        </Card>
      )}

      {isAnalyzing && (
        <Card className="p-6 mb-6">
          <View className="items-center">
            <Text className="text-lg font-semibold mb-2">
              Analyzing Nutrition
            </Text>
            <Text className="text-muted-foreground text-center">
              Our AI is identifying ingredients and calculating nutrition
              values...
            </Text>
          </View>
        </Card>
      )}
    </View>
  );
};
