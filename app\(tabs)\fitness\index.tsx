import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { theme } from "@/constants/theme";
import {
  Calendar,
  Dumbbell,
  Target,
  Plus,
  Award,
  Flame,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import Animated, { FadeInDown } from "react-native-reanimated";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Header } from "@/components/ui/Header";

const AnimatedView = Animated.createAnimatedComponent(View);

export default function FitnessDashboard() {
  const router = useRouter();
  const workouts =
    useQuery(api.fitness.getWorkouts, { completed: false }) || [];
  const weeklyStats = useQuery(api.fitness.getWeeklyStats) || {
    completedWorkouts: 0,
    totalPlanned: 0,
    streakDays: 0,
    caloriesBurned: 0,
  };

  const upcomingWorkouts = workouts
    .filter((w) => !w.completed && new Date(w.scheduledDate) >= new Date())
    .slice(0, 3);

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Fitness Dashboard"
        rightElement={<Plus size={24} color={theme.colors.white} />}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Weekly Stats */}
        <AnimatedView entering={FadeInDown.delay(200)} className="mx-6 mb-6">
          <Card className="bg-white border border-gray-200">
            <CardContent className="p-6">
              <View className="flex-row items-center justify-between mb-4">
                <Text className="text-lg font-semibold text-gray-900">
                  This Week's Progress
                </Text>
                <Badge variant="secondary" className="bg-blue-50">
                  <Text className="text-blue-700 text-xs font-medium">
                    Week {Math.ceil(new Date().getDate() / 7)}
                  </Text>
                </Badge>
              </View>

              {/* Progress Bar */}
              <View className="mb-6">
                <View className="flex-row justify-between items-center mb-2">
                  <Text className="text-sm text-gray-600">
                    Weekly Goal Progress
                  </Text>
                  <Text className="text-sm font-medium text-gray-900">
                    {weeklyStats.completedWorkouts}/{weeklyStats.totalPlanned}
                  </Text>
                </View>
                <Progress
                  value={
                    weeklyStats.totalPlanned > 0
                      ? (weeklyStats.completedWorkouts /
                          weeklyStats.totalPlanned) *
                        100
                      : 0
                  }
                  className="h-2"
                />
              </View>

              {/* Stats Grid */}
              <View className="flex-row justify-between">
                <View className="flex-1 items-center">
                  <View className="w-12 h-12 rounded-full bg-blue-50 items-center justify-center mb-2">
                    <Dumbbell size={20} color="#3b82f6" />
                  </View>
                  <Text className="text-2xl font-bold text-gray-900">
                    {weeklyStats.completedWorkouts}
                  </Text>
                  <Text className="text-xs text-gray-500 text-center">
                    Completed{"\n"}Workouts
                  </Text>
                </View>

                <View className="flex-1 items-center">
                  <View className="w-12 h-12 rounded-full bg-green-50 items-center justify-center mb-2">
                    <Target size={20} color="#10b981" />
                  </View>
                  <Text className="text-2xl font-bold text-gray-900">
                    {weeklyStats.streakDays}
                  </Text>
                  <Text className="text-xs text-gray-500 text-center">
                    Day{"\n"}Streak
                  </Text>
                </View>

                <View className="flex-1 items-center">
                  <View className="w-12 h-12 rounded-full bg-orange-50 items-center justify-center mb-2">
                    <Flame size={20} color="#f97316" />
                  </View>
                  <Text className="text-2xl font-bold text-gray-900">
                    {weeklyStats.caloriesBurned}
                  </Text>
                  <Text className="text-xs text-gray-500 text-center">
                    Calories{"\n"}Burned
                  </Text>
                </View>
              </View>
            </CardContent>
          </Card>
        </AnimatedView>

        {/* Quick Actions */}
        <AnimatedView entering={FadeInDown.delay(300)} className="mx-6 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Quick Actions
          </Text>
          <View className="flex-row gap-4">
            <TouchableOpacity
              className="flex-1"
              onPress={() => router.push("/fitness/schedule")}
            >
              <Card className="bg-white border border-gray-200">
                <CardContent className="p-4 items-center">
                  <View className="w-12 h-12 rounded-full bg-blue-50 items-center justify-center mb-3">
                    <Calendar size={24} color="#3b82f6" />
                  </View>
                  <Text className="font-semibold text-gray-900 text-center mb-1">
                    Schedule
                  </Text>
                  <Text className="text-xs text-gray-500 text-center">
                    View workout calendar
                  </Text>
                </CardContent>
              </Card>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-1"
              onPress={() => router.push("/fitness/templates")}
            >
              <Card className="bg-white border border-gray-200">
                <CardContent className="p-4 items-center">
                  <View className="w-12 h-12 rounded-full bg-purple-50 items-center justify-center mb-3">
                    <Dumbbell size={24} color="#8b5cf6" />
                  </View>
                  <Text className="font-semibold text-gray-900 text-center mb-1">
                    Templates
                  </Text>
                  <Text className="text-xs text-gray-500 text-center">
                    Pre-made workouts
                  </Text>
                </CardContent>
              </Card>
            </TouchableOpacity>
          </View>
        </AnimatedView>

        {/* Goals Information */}
        <AnimatedView entering={FadeInDown.delay(350)} className="mx-6 mb-6">
          <Card className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200">
            <CardContent className="p-4">
              <View className="flex-row items-center mb-3">
                <View className="w-10 h-10 rounded-full bg-indigo-100 items-center justify-center mr-3">
                  <Award size={20} color="#6366f1" />
                </View>
                <View className="flex-1">
                  <Text className="font-semibold text-gray-900 mb-1">
                    About Goals in Our App
                  </Text>
                  <Text className="text-xs text-gray-600">
                    Wellbeing & Fitness Tracking
                  </Text>
                </View>
              </View>
              <Text className="text-sm text-gray-700 mb-3">
                Goals help you set and track targets for wellbeing activities
                like sleep, meditation, mood, and stress management. They
                provide structure and motivation for your health journey.
              </Text>
              <TouchableOpacity
                onPress={() => router.push("/(tabs)/wellbeing/goals")}
                className="bg-indigo-600 rounded-lg px-4 py-2 self-start"
              >
                <Text className="text-white text-sm font-medium">
                  View My Goals
                </Text>
              </TouchableOpacity>
            </CardContent>
          </Card>
        </AnimatedView>

        {/* Upcoming Workouts */}
        <AnimatedView
          style={styles.upcomingSection}
          entering={FadeInDown.delay(400)}
        >
          <Text style={styles.sectionTitle}>Upcoming Workouts</Text>
          {upcomingWorkouts.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>
                No upcoming workouts scheduled
              </Text>
              <TouchableOpacity
                style={styles.scheduleButton}
                onPress={() => router.push("/fitness/create-workout")}
              >
                <Text style={styles.scheduleButtonText}>Schedule Workout</Text>
              </TouchableOpacity>
            </View>
          ) : (
            upcomingWorkouts.map((workout, index) => (
              <TouchableOpacity
                key={workout._id}
                style={styles.workoutCard}
                onPress={() => router.push(`/fitness/workout/${workout._id}`)}
              >
                <View style={styles.workoutHeader}>
                  <Text style={styles.workoutTitle}>{workout.name}</Text>
                  <Text style={styles.workoutDate}>
                    {new Date(workout.scheduledDate).toLocaleDateString()}
                  </Text>
                </View>
                <Text style={styles.workoutType}>{workout.type}</Text>
                <Text style={styles.workoutDuration}>
                  {workout.estimatedDuration} min
                </Text>
              </TouchableOpacity>
            ))
          )}
        </AnimatedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
    paddingVertical: theme.spacing.l,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.gray[900],
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  statsCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.xl,
    ...theme.shadows.medium,
  },
  statsTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.l,
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statItem: {
    flex: 1,
    alignItems: "center",
  },
  statValue: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
    marginTop: theme.spacing.s,
  },
  statLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginTop: theme.spacing.xs,
  },
  statDivider: {
    width: 1,
    height: "80%",
    backgroundColor: theme.colors.gray[200],
  },
  quickActions: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.l,
  },
  actionGrid: {
    flexDirection: "row",
    gap: theme.spacing.l,
  },
  actionCard: {
    flex: 1,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    alignItems: "center",
    ...theme.shadows.small,
  },
  actionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginTop: theme.spacing.m,
  },
  actionSubtitle: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginTop: theme.spacing.xs,
    textAlign: "center",
  },
  upcomingSection: {
    marginBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.l,
  },
  emptyState: {
    alignItems: "center",
    padding: theme.spacing.xl,
  },
  emptyText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.l,
  },
  scheduleButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.m,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.m,
  },
  scheduleButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
  },
  workoutCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
    ...theme.shadows.small,
  },
  workoutHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.s,
  },
  workoutTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
  },
  workoutDate: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
  },
  workoutType: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs,
  },
  workoutDuration: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
  },
});
