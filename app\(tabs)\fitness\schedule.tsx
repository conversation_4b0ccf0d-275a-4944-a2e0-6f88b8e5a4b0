import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { theme } from "@/constants/theme";
import { ChevronLeft, ChevronRight, Plus } from "lucide-react-native";
import { useRouter } from "expo-router";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Header } from "@/components/ui/Header";

export default function WorkoutSchedule() {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const workouts = useQuery(api.fitness.getWorkouts, {}) || [];

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const current = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  const calendarDays = generateCalendarDays();
  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const getWorkoutsForDate = (date: Date) => {
    const dateString = date.toISOString().split("T")[0];
    return workouts.filter((w) => w.scheduledDate.startsWith(dateString));
  };

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() + (direction === "next" ? 1 : -1));
    setSelectedDate(newDate);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === selectedDate.getMonth();
  };

  const getWorkoutTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "strength":
        return theme.colors.primary;
      case "cardio":
        return theme.colors.accent;
      case "flexibility":
        return theme.colors.secondary;
      case "hiit":
        return theme.colors.error;
      default:
        return theme.colors.gray[500];
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header showBackButton title="Workout Schedule" />
      <View style={styles.header}>
        <View style={styles.monthNavigation}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => navigateMonth("prev")}
          >
            <ChevronLeft size={24} color={theme.colors.gray[600]} />
          </TouchableOpacity>

          <Text style={styles.monthTitle}>
            {monthNames[selectedDate.getMonth()]} {selectedDate.getFullYear()}
          </Text>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => navigateMonth("next")}
          >
            <ChevronRight size={24} color={theme.colors.gray[600]} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push("/fitness/create-workout")}
        >
          <Plus size={20} color={theme.colors.white} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Calendar Header */}
        <View style={styles.calendarHeader}>
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
            <Text key={day} style={styles.dayHeader}>
              {day}
            </Text>
          ))}
        </View>

        {/* Calendar Grid */}
        <View style={styles.calendarGrid}>
          {calendarDays.map((date, index) => {
            const dayWorkouts = getWorkoutsForDate(date);
            const isCurrentMonthDay = isCurrentMonth(date);
            const isTodayDate = isToday(date);
            const isSelectedDate =
              date.toDateString() === selectedDate.toDateString();

            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.calendarDay,
                  isTodayDate && styles.todayDay,
                  isSelectedDate && styles.selectedDay,
                  !isCurrentMonthDay && styles.otherMonthDay,
                ]}
                onPress={() => {
                  setSelectedDate(new Date(date));
                }}
              >
                <Text
                  style={[
                    styles.dayNumber,
                    isTodayDate && styles.todayText,
                    isSelectedDate && styles.selectedText,
                    !isCurrentMonthDay && styles.otherMonthText,
                  ]}
                >
                  {date.getDate()}
                </Text>

                {/* Workout indicators */}
                <View style={styles.workoutIndicators}>
                  {dayWorkouts.slice(0, 3).map((workout, idx) => (
                    <View
                      key={idx}
                      style={[
                        styles.workoutDot,
                        { backgroundColor: getWorkoutTypeColor(workout.type) },
                      ]}
                    />
                  ))}
                  {dayWorkouts.length > 3 && (
                    <Text style={styles.moreIndicator}>
                      +{dayWorkouts.length - 3}
                    </Text>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Workout Legend */}
        <View style={styles.legend}>
          <Text style={styles.legendTitle}>Workout Types</Text>
          <View style={styles.legendItems}>
            {[
              { type: "Strength", color: theme.colors.primary },
              { type: "Cardio", color: theme.colors.accent },
              { type: "Flexibility", color: theme.colors.secondary },
              { type: "HIIT", color: theme.colors.error },
            ].map((item) => (
              <View key={item.type} style={styles.legendItem}>
                <View
                  style={[styles.legendDot, { backgroundColor: item.color }]}
                />
                <Text style={styles.legendText}>{item.type}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Selected Date Workouts */}
        <View style={styles.todaySection}>
          <Text style={styles.sectionTitle}>
            {selectedDate.toDateString() === new Date().toDateString()
              ? "Today's Workouts"
              : `Workouts for ${selectedDate.toLocaleDateString("en-US", {
                  weekday: "long",
                  month: "short",
                  day: "numeric",
                })}`}
          </Text>
          {getWorkoutsForDate(selectedDate).length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>
                {selectedDate.toDateString() === new Date().toDateString()
                  ? "No workouts scheduled for today"
                  : `No workouts scheduled for ${selectedDate.toLocaleDateString("en-US", { month: "short", day: "numeric" })}`}
              </Text>
            </View>
          ) : (
            getWorkoutsForDate(selectedDate).map((workout) => (
              <TouchableOpacity
                key={workout._id}
                style={styles.workoutCard}
                onPress={() => router.push(`/fitness/workout/${workout._id}`)}
              >
                <View style={styles.workoutHeader}>
                  <Text style={styles.workoutTitle}>{workout.name}</Text>
                  <View
                    style={[
                      styles.workoutTypeBadge,
                      {
                        backgroundColor: `${getWorkoutTypeColor(workout.type)}20`,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.workoutTypeText,
                        { color: getWorkoutTypeColor(workout.type) },
                      ]}
                    >
                      {workout.type}
                    </Text>
                  </View>
                </View>
                <Text style={styles.workoutDuration}>
                  {workout.estimatedDuration} minutes
                </Text>
                {workout.completed && (
                  <Text style={styles.completedText}>✓ Completed</Text>
                )}
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: theme.spacing.l,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  monthNavigation: {
    flexDirection: "row",
    alignItems: "center",
  },
  navButton: {
    padding: theme.spacing.s,
  },
  monthTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginHorizontal: theme.spacing.l,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    flex: 1,
    padding: theme.spacing.l,
  },
  calendarHeader: {
    flexDirection: "row",
    marginBottom: theme.spacing.m,
  },
  dayHeader: {
    flex: 1,
    textAlign: "center",
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    paddingVertical: theme.spacing.s,
  },
  calendarGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: theme.spacing.xl,
  },
  calendarDay: {
    width: "14.28%",
    aspectRatio: 1,
    padding: theme.spacing.xs,
    alignItems: "center",
    justifyContent: "flex-start",
    borderWidth: 1,
    borderColor: theme.colors.gray[100],
  },
  todayDay: {
    backgroundColor: `${theme.colors.primary}10`,
    borderColor: theme.colors.primary,
  },
  selectedDay: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  otherMonthDay: {
    opacity: 0.3,
  },
  dayNumber: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[900],
  },
  todayText: {
    color: theme.colors.primary,
    fontFamily: theme.typography.fontFamily.bold,
  },
  selectedText: {
    color: theme.colors.white,
    fontFamily: theme.typography.fontFamily.bold,
  },
  otherMonthText: {
    color: theme.colors.gray[400],
  },
  workoutIndicators: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    marginTop: theme.spacing.xs,
  },
  workoutDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    margin: 1,
  },
  moreIndicator: {
    fontSize: 8,
    color: theme.colors.gray[600],
  },
  legend: {
    marginBottom: theme.spacing.xl,
  },
  legendTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.m,
  },
  legendItems: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: theme.spacing.l,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: theme.spacing.s,
  },
  legendText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[700],
  },
  todaySection: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.l,
  },
  emptyState: {
    alignItems: "center",
    padding: theme.spacing.xl,
  },
  emptyText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
  },
  workoutCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
    ...theme.shadows.small,
  },
  workoutHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.s,
  },
  workoutTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    flex: 1,
  },
  workoutTypeBadge: {
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.m,
  },
  workoutTypeText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.xs,
  },
  workoutDuration: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.xs,
  },
  completedText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.success,
  },
});
