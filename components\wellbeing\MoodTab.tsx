import React, { useState } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import {
  Heart,
  Plus,
  Smile,
  Frown,
  Meh,
  Zap,
  Cloud,
  Moon,
  Leaf,
  TrendingUp,
} from "lucide-react-native";

const moodOptions = [
  { mood: "happy", icon: Smile, color: "#22c55e", label: "Happy" },
  { mood: "excited", icon: Zap, color: "#f59e0b", label: "Excited" },
  { mood: "calm", icon: Leaf, color: "#06b6d4", label: "Calm" },
  { mood: "neutral", icon: Meh, color: "#6b7280", label: "Neutral" },
  { mood: "tired", icon: Moon, color: "#8b5cf6", label: "Tired" },
  { mood: "stressed", icon: Cloud, color: "#ef4444", label: "Stressed" },
  { mood: "anxious", icon: Cloud, color: "#f97316", label: "Anxious" },
  { mood: "sad", icon: Frown, color: "#3b82f6", label: "Sad" },
];

export function MoodTab() {
  const [showMoodForm, setShowMoodForm] = useState(false);

  const currentMood = useQuery(api.mood.getCurrentMood);
  const recentMoodEntries = useQuery(api.mood.getMoodEntries, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  const getMoodOption = (moodName: string) => {
    return moodOptions.find((option) => option.mood === moodName);
  };

  const getMoodStats = () => {
    if (!recentMoodEntries || recentMoodEntries.length === 0) return null;

    const moodCounts = recentMoodEntries.reduce(
      (acc, entry) => {
        acc[entry.mood] = (acc[entry.mood] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const mostCommon = Object.entries(moodCounts).reduce((a, b) =>
      moodCounts[a[0]] > moodCounts[b[0]] ? a : b
    );

    return {
      totalEntries: recentMoodEntries.length,
      mostCommon: mostCommon[0],
      averageIntensity:
        recentMoodEntries.reduce(
          (sum, entry) => sum + (entry.intensity || 0),
          0
        ) / recentMoodEntries.length,
    };
  };

  const stats = getMoodStats();

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      {/* Quick Log Button */}
      <View className="px-6 mb-6">
        <TouchableOpacity
          onPress={() => setShowMoodForm(true)}
          className="bg-pink-600 rounded-2xl p-4 flex-row items-center justify-center"
        >
          <Plus size={20} color="#ffffff" />
          <Text className="text-white font-semibold text-base ml-2">
            Log Mood
          </Text>
        </TouchableOpacity>
      </View>

      {/* Current Mood */}
      <View className="px-6 mb-6">
        <Text className="text-lg font-semibold text-gray-900 mb-4">
          Current Mood
        </Text>
        <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          {currentMood ? (
            <View className="items-center">
              {(() => {
                const moodOption = getMoodOption(currentMood.mood);
                const Icon = moodOption?.icon || Heart;
                const color = moodOption?.color || "#6b7280";
                return (
                  <>
                    <View
                      className="w-16 h-16 rounded-2xl items-center justify-center mb-3"
                      style={{ backgroundColor: `${color}20` }}
                    >
                      <Icon size={32} color={color} />
                    </View>
                    <Text className="text-xl font-bold text-gray-900 mb-1">
                      {moodOption?.label || currentMood.mood}
                    </Text>
                    <Text className="text-gray-600 text-sm mb-2">
                      Intensity: {currentMood.intensity}/10
                    </Text>
                    <Text className="text-gray-500 text-xs">
                      {new Date(currentMood._creationTime).toLocaleString()}
                    </Text>
                  </>
                );
              })()}
            </View>
          ) : (
            <View className="items-center py-4">
              <View className="w-16 h-16 rounded-2xl bg-gray-100 items-center justify-center mb-3">
                <Heart size={32} color="#6b7280" />
              </View>
              <Text className="text-gray-600 text-base">
                No mood logged today
              </Text>
              <Text className="text-gray-500 text-sm">
                Tap above to log your mood
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Mood Stats */}
      {stats && (
        <View className="px-6 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            This Week
          </Text>
          <View className="flex-row mb-3">
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mr-2">
              <View className="items-center">
                <Text className="text-2xl font-bold text-gray-900">
                  {stats.totalEntries}
                </Text>
                <Text className="text-gray-600 text-sm">Entries</Text>
              </View>
            </View>
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 ml-2">
              <View className="items-center">
                <Text className="text-2xl font-bold text-gray-900">
                  {stats.averageIntensity.toFixed(1)}
                </Text>
                <Text className="text-gray-600 text-sm">Avg Intensity</Text>
              </View>
            </View>
          </View>
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <Text className="font-semibold text-gray-900 mb-2">
              Most Common Mood
            </Text>
            {(() => {
              const moodOption = getMoodOption(stats.mostCommon);
              const Icon = moodOption?.icon || Heart;
              const color = moodOption?.color || "#6b7280";
              return (
                <View className="flex-row items-center">
                  <View
                    className="w-10 h-10 rounded-xl items-center justify-center mr-3"
                    style={{ backgroundColor: `${color}20` }}
                  >
                    <Icon size={20} color={color} />
                  </View>
                  <Text className="text-gray-900 font-medium">
                    {moodOption?.label || stats.mostCommon}
                  </Text>
                </View>
              );
            })()}
          </View>
        </View>
      )}

      {/* Recent Entries */}
      {recentMoodEntries && recentMoodEntries.length > 0 && (
        <View className="px-6 mb-6">
          <Text className="text-lg font-semibold text-gray-900 mb-4">
            Recent Entries
          </Text>
          <View className="bg-white rounded-2xl shadow-sm border border-gray-100">
            {recentMoodEntries.slice(0, 5).map((entry, index) => {
              const moodOption = getMoodOption(entry.mood);
              const Icon = moodOption?.icon || Heart;
              const color = moodOption?.color || "#6b7280";

              return (
                <View
                  key={entry._id}
                  className={`p-4 ${index > 0 ? "border-t border-gray-100" : ""}`}
                >
                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center flex-1">
                      <View
                        className="w-8 h-8 rounded-lg items-center justify-center mr-3"
                        style={{ backgroundColor: `${color}20` }}
                      >
                        <Icon size={16} color={color} />
                      </View>
                      <View className="flex-1">
                        <Text className="font-medium text-gray-900">
                          {moodOption?.label || entry.mood}
                        </Text>
                        <Text className="text-gray-600 text-sm">
                          Intensity: {entry.intensity}/10
                        </Text>
                      </View>
                    </View>
                    <Text className="text-gray-500 text-xs">
                      {new Date(entry._creationTime).toLocaleDateString()}
                    </Text>
                  </View>
                  {entry.note && (
                    <Text className="text-gray-600 text-sm mt-2 ml-11">
                      {entry.note}
                    </Text>
                  )}
                </View>
              );
            })}
          </View>
        </View>
      )}
    </ScrollView>
  );
}
