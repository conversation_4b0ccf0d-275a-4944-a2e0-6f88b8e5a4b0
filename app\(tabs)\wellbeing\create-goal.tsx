import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from "react-native";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import {
  Target,
  Calendar,
  Hash,
  FileText,
  Save,
  Info,
} from "lucide-react-native";
import { Header } from "@/components/ui/Header";
import { useRouter } from "expo-router";
import { Stack } from "expo-router";
import DateTimePicker from "@react-native-community/datetimepicker";
import {
  wellbeingGoalRules,
  getGoalRule,
  formatGoalValue,
  WellbeingGoalType,
} from "@/data/wellbeingGoalsData";

export default function CreateWellbeingGoalScreen() {
  const router = useRouter();
  const [selectedGoalType, setSelectedGoalType] =
    useState<WellbeingGoalType>("sleep_duration");
  const [targetValue, setTargetValue] = useState("8");
  const [description, setDescription] = useState("");
  const [endDate, setEndDate] = useState(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  );
  const [showDatePicker, setShowDatePicker] = useState(false);

  const selectedRule = getGoalRule(selectedGoalType);
  const startDate = new Date();

  const createGoal = useMutation(api.wellbeingGoals.createWellbeingGoal);

  const handleSave = async () => {
    if (!selectedRule || !targetValue.trim()) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    const parsedValue = parseFloat(targetValue);
    if (
      isNaN(parsedValue) ||
      parsedValue < selectedRule.minValue ||
      parsedValue > selectedRule.maxValue
    ) {
      Alert.alert(
        "Invalid Value",
        `Please enter a value between ${selectedRule.minValue} and ${selectedRule.maxValue}`
      );
      return;
    }

    try {
      await createGoal({
        type: selectedGoalType as any,
        title: selectedRule.title,
        description: description || selectedRule.description,
        targetValue: parsedValue,
        unit: selectedRule.unit,
        startDate: startDate.toISOString().split("T")[0],
        endDate: endDate.toISOString().split("T")[0],
      });

      router.back();
    } catch (error) {
      Alert.alert("Error", "Failed to save goal");
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === "ios");
    if (selectedDate) {
      setEndDate(selectedDate);
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      <Stack.Screen options={{ headerShown: false }} />
      <Header title="Create New Goal" showBackButton={true} />
      <ScrollView className="flex-1 p-6">
        {/* Goal Type Selection */}
        <View className="mb-8">
          <View className="mb-4">
            <Text className="text-lg font-semibold text-gray-900 mb-1">
              Choose Your Goal Type
            </Text>
            <Text className="text-sm text-gray-600">
              Select the type of wellbeing goal you want to track
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 24 }}
            style={{ marginHorizontal: -24 }}
          >
            <View className="flex-row space-x-3">
              {wellbeingGoalRules.map((rule) => (
                <TouchableOpacity
                  key={rule.type}
                  onPress={() => {
                    setSelectedGoalType(rule.type);
                    setTargetValue(rule.defaultTarget.toString());
                  }}
                  className={`px-4 py-4 rounded-2xl border-2 min-w-[120px] ${
                    selectedGoalType === rule.type
                      ? "bg-gray-900 border-gray-900 shadow-lg"
                      : "bg-white border-gray-100 shadow-sm"
                  }`}
                >
                  <Text className="text-2xl mb-2 text-center">{rule.icon}</Text>
                  <Text
                    className={`text-xs font-medium text-center leading-4 ${
                      selectedGoalType === rule.type
                        ? "text-white"
                        : "text-gray-700"
                    }`}
                  >
                    {rule.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Benefits Section */}
        {selectedRule && (
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-3">
              Why This Goal Matters
            </Text>
            <View className="space-y-2">
              {selectedRule.benefits.map((benefit, index) => (
                <View key={index} className="flex-row items-center">
                  <View className="w-2 h-2 bg-gray-900 rounded-full mr-3" />
                  <Text className="text-sm text-gray-700 flex-1">
                    {benefit}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Goal Details Form */}
        <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
          <View className="mb-6">
            <Text className="text-lg font-semibold text-gray-900 mb-1">
              {selectedRule?.title} Goal
            </Text>
            <Text className="text-sm text-gray-600">
              {selectedRule?.description}
            </Text>
          </View>

          {/* Goal Info Card */}
          {selectedRule && (
            <View className="bg-gray-50 rounded-xl p-4 mb-5 border border-gray-100">
              <View className="flex-row items-center mb-2">
                <Info size={16} color="#6B7280" />
                <Text className="text-sm font-medium text-gray-700 ml-2">
                  Goal Information
                </Text>
              </View>
              <Text className="text-xs text-gray-600 mb-2">
                {selectedRule.helpText}
              </Text>
              <Text className="text-xs text-gray-500">
                Tracking: {selectedRule.trackingFrequency} • Category:{" "}
                {selectedRule.category}
              </Text>
            </View>
          )}

          {/* Custom Description */}
          <View className="mb-5">
            <View className="flex-row items-center mb-2">
              <FileText size={16} color="#6B7280" />
              <Text className="text-sm font-medium text-gray-700 ml-2">
                Personal Notes (Optional)
              </Text>
            </View>
            <TextInput
              value={description}
              onChangeText={setDescription}
              placeholder="Add your personal motivation or specific details..."
              multiline
              numberOfLines={3}
              textAlignVertical="top"
              className="border border-gray-200 rounded-xl px-4 py-3.5 text-gray-900 bg-gray-50 focus:bg-white focus:border-gray-400 min-h-[80px]"
            />
          </View>

          {/* Target Value */}
          <View className="mb-5">
            <View className="flex-row items-center mb-2">
              <Hash size={16} color="#6B7280" />
              <Text className="text-sm font-medium text-gray-700 ml-2">
                Target Value * ({selectedRule?.unit})
              </Text>
            </View>
            <TextInput
              value={targetValue}
              onChangeText={setTargetValue}
              placeholder={selectedRule?.placeholder}
              keyboardType="numeric"
              className="border border-gray-200 rounded-xl px-4 py-3.5 text-gray-900 bg-gray-50 focus:bg-white focus:border-gray-400"
            />
            {selectedRule && (
              <Text className="text-xs text-gray-500 mt-1">
                Range: {selectedRule.minValue} - {selectedRule.maxValue}{" "}
                {selectedRule.unit}
              </Text>
            )}
          </View>

          {/* End Date */}
          <View className="mb-0">
            <View className="flex-row items-center mb-2">
              <Calendar size={16} color="#6B7280" />
              <Text className="text-sm font-medium text-gray-700 ml-2">
                Target End Date
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => setShowDatePicker(true)}
              className="border border-gray-200 rounded-xl px-4 py-3.5 bg-gray-50"
            >
              <Text className="text-gray-900">
                {endDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={endDate}
                mode="date"
                display={Platform.OS === "ios" ? "spinner" : "default"}
                onChange={handleDateChange}
                minimumDate={new Date()}
              />
            )}
          </View>
        </View>
      </ScrollView>

      {/* Footer */}
      <View className="p-6 border-t border-gray-100 bg-white">
        <TouchableOpacity
          onPress={handleSave}
          className="flex-row items-center justify-center py-4 px-6 rounded-2xl bg-gray-900 shadow-lg"
        >
          <Save size={20} color="#FFFFFF" />
          <Text className="text-center font-semibold text-white text-base ml-2">
            Create Goal
          </Text>
        </TouchableOpacity>
        <Text className="text-xs text-gray-500 text-center mt-3">
          Track your progress and build healthy habits
        </Text>
      </View>
    </View>
  );
}
