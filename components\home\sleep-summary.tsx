import { View, TouchableOpacity } from "react-native";
import React from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardDescription,
} from "../ui/card";
import { Text } from "../ui/text";
import { Button } from "../ui/button";
import {
  Moon,
  Bed,
  Clock,
  Activity,
  Star,
  ThumbsUp,
  Plus,
} from "lucide-react-native";
import { SkeletonLayout } from "../ui/Skeleton";
import { formatDate, cn } from "@/lib/utils";
import { router } from "expo-router";

interface SleepDataPoint {
  value: number;
  label: string;
  color?: string;
}

const SleepSummary = () => {
  const today = new Date();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(today.getDate() - 7);

  const averageSleepDuration = useQuery(api.sleep.getAverageSleepDuration, {
    startDate: sevenDaysAgo.toISOString().split("T")[0],
    endDate: today.toISOString().split("T")[0],
  });
  const lastSleepEntry = useQuery(api.sleep.getLastSleepEntry);
  const last7DaysSleepSummary = useQuery(api.sleep.getLast7DaysSleepSummary);

  if (
    averageSleepDuration === undefined ||
    lastSleepEntry === undefined ||
    last7DaysSleepSummary === undefined
  ) {
    return <SkeletonLayout showGrid />;
  }

  // Calculate sleep quality percentage and color
  const getQualityColor = (rating: number) => {
    if (rating >= 4) return "bg-primary";
    if (rating >= 3) return "bg-blue-500";
    return "bg-amber-500";
  };

  const qualityPercentage = lastSleepEntry
    ? (lastSleepEntry.qualityRating / 5) * 100
    : 0;
  const qualityColor = lastSleepEntry
    ? getQualityColor(lastSleepEntry.qualityRating)
    : "bg-muted";

  return (
    <Card className={cn("w-full border-0")}>
      <CardHeader className="rounded-md p-4 border border-border">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center gap-4">
            <View className="bg-secondary p-3 rounded-xl">
              <Moon color={"indigo"} size={28} />
            </View>
            <View>
              <CardTitle className="text-xl font-extrabold tracking-tight ">
                Sleep Summary
              </CardTitle>
              <CardDescription className="text-base text-muted-foreground">
                Your recent sleep insights
              </CardDescription>
            </View>
          </View>
        </View>
      </CardHeader>
      <CardContent className="mt-3 p-0 space-y-6 pb-4">
        <View className="flex-row gap-3 mb-2">
          {/* Average Sleep */}
          <View className={" flex-1 border border-border p-4 rounded-md"}>
            <View className="flex-row items-center justify-between mb-2">
              <Text className="text-base font-semibold text-muted-foreground">
                Avg. Sleep
              </Text>
              <Clock color="green" className="text-primary/70" size={17} />
            </View>
            <Text className="text-3xl font-extrabold text-muted-foreground mb-1">
              {averageSleepDuration.toFixed(1)}h
            </Text>
            <Text className="text-xs text-muted-foreground/80 mt-1">
              Past 7 days
            </Text>
          </View>

          {/* Last Sleep Duration */}
          <View className={" flex-1 border border-border p-4 rounded-md"}>
            <View className="flex-row items-center justify-between mb-2">
              <Text className="text-base font-semibold text-muted-foreground">
                Last Sleep
              </Text>
              <Bed color="brown" className="text-primary/70" size={18} />
            </View>
            {lastSleepEntry ? (
              <>
                <Text className="text-3xl font-black text-muted-foreground mb-1">
                  {lastSleepEntry.durationHours}h
                </Text>
                <Text className="text-xs text-muted-foreground/80 mt-1">
                  {formatDate(lastSleepEntry.date)}
                </Text>
              </>
            ) : (
              <View className="items-center">
                <Text className="text-sm text-muted-foreground mb-2">
                  No data
                </Text>
                <TouchableOpacity
                  onPress={() => router.push("/(tabs)/wellbeing/sleep")}
                  className="flex-row items-center gap-1 bg-secondary px-2 py-1 rounded-md"
                >
                  <Plus size={12} color="#6b7280" />
                  <Text className="text-xs text-muted-foreground">
                    Log Sleep
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>

        {/* Sleep Quality */}
        {lastSleepEntry && (
          <View
            className={
              " flex-1 bg-card  border border-border p-4 rounded-md mb-2"
            }
          >
            <View className="flex-row items-center justify-between mb-3">
              <View className="flex-row gap-2 items-center">
                <ThumbsUp
                  size={18}
                  color="orange"
                  className="w-5 h-5 text-amber-400 mr-2"
                />
                <Text className="text-base font-semibold">Sleep Quality</Text>
              </View>
              <View className="flex-row items-center space-x-1.5">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    size={18}
                    fill={
                      star <= lastSleepEntry.qualityRating ? "orange" : "gray"
                    }
                    key={star}
                    color={
                      star <= lastSleepEntry.qualityRating ? "orange" : "gray"
                    }
                    className={`w-4 h-4 ${star <= lastSleepEntry.qualityRating ? "text-amber-400 fill-amber-400" : "text-muted-foreground/30"}`}
                  />
                ))}
                <Text className="ml-2 text-base font-semibold">
                  {lastSleepEntry.qualityRating}/5
                </Text>
              </View>
            </View>
            <View className="w-full h-4 bg-muted/40 rounded-full overflow-hidden">
              <View
                className={cn("h-full rounded-full", qualityColor)}
                style={{ width: `${qualityPercentage}%` }}
              />
            </View>
            <View className="flex-row justify-between mt-2">
              <Text className="text-xs text-muted-foreground">Poor</Text>
              <Text className="text-xs text-muted-foreground">Excellent</Text>
            </View>
          </View>
        )}
      </CardContent>
    </Card>
  );
};

export default SleepSummary;
