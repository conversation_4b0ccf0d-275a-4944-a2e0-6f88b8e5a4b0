import React, { use<PERSON><PERSON>back, use<PERSON>emo, useRef, useState } from "react";
import { View, TouchableOpacity } from "react-native";
import BottomSheet, { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Smile,
  Frown,
  Meh,
  Zap,
  Cloud,
  Heart,
  Moon,
  Leaf,
  X,
  Brain,
} from "lucide-react-native";
import { cn } from "@/lib/utils";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import moment from "moment";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react-native";

interface LogMoodSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

const moodOptions = [
  "happy",
  "sad",
  "anxious",
  "angry",
  "neutral",
  "excited",
  "tired",
  "stressed",
  "calm",
  "other",
];

const getMoodIcon = (
  mood: string,
  size: number = 20,
  color: string = "#fff"
) => {
  switch (mood) {
    case "happy":
      return <Smile size={size} color={color} />;
    case "sad":
      return <Frown size={size} color={color} />;
    case "anxious":
      return <Cloud size={size} color={color} />;
    case "angry":
      return <Zap size={size} color={color} />;
    case "excited":
      return <Heart size={size} color={color} />;
    case "tired":
      return <Moon size={size} color={color} />;
    case "stressed":
      return <Cloud size={size} color={color} />;
    case "calm":
      return <Leaf size={size} color={color} />;
    case "neutral":
    case "other":
    default:
      return <Meh size={size} color={color} />;
  }
};

const moodFeedback = [
  { min: 1, max: 2, text: "Very Low", color: "#64748b" },
  { min: 3, max: 4, text: "Low", color: "#60a5fa" },
  { min: 5, max: 6, text: "Moderate", color: "#facc15" },
  { min: 7, max: 8, text: "High", color: "#34d399" },
  { min: 9, max: 10, text: "Very High", color: "#f472b6" },
];

const getMoodFeedback = (value: number) => {
  return (
    moodFeedback.find((f) => value >= f.min && value <= f.max) ||
    moodFeedback[2]
  );
};

const moodColors: Record<string, string> = {
  happy: "#facc15", // yellow
  sad: "#60a5fa", // blue
  anxious: "#a78bfa", // purple
  angry: "#f87171", // red
  neutral: "#a3a3a3", // gray
  excited: "#f472b6", // pink
  tired: "#cbd5e1", // light blue/gray
  stressed: "#fbbf24", // orange
  calm: "#34d399", // green
  other: "#818cf8", // indigo
};

const LogMoodSheet = ({ isOpen, onClose }: LogMoodSheetProps) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ["60%", "85%"], []);

  const createMoodEntry = useMutation(api.mood.createMoodEntry);

  const [selectedMood, setSelectedMood] = useState<string | null>(null);
  const [intensity, setIntensity] = useState<number>(5);
  const [note, setNote] = useState<string>("");
  const [isLogging, setIsLogging] = useState(false);
  const [logSuccess, setLogSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose();
        resetForm();
      }
    },
    [onClose]
  );

  const resetForm = () => {
    setSelectedMood(null);
    setIntensity(5);
    setNote("");
    setIsLogging(false);
    setLogSuccess(false);
    setError(null);
  };

  const handleLogMood = async () => {
    if (!selectedMood) {
      setError("Please select a mood.");
      return;
    }

    setIsLogging(true);
    setError(null);
    try {
      await createMoodEntry({
        date: moment().format("YYYY-MM-DD"),
        mood: selectedMood as any, // Cast to any as v.union types are strict
      });
      setLogSuccess(true);
      setTimeout(() => {
        bottomSheetRef.current?.close();
      }, 1500); // Close after a short delay to show success
    } catch (err) {
      console.error("Failed to log mood:", err);
      setError("Failed to log mood. Please try again.");
    } finally {
      setIsLogging(false);
    }
  };

  const handleMoodPress = (mood: string) => {
    setSelectedMood(mood);
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={isOpen ? 1 : -1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose={true}
      backgroundStyle={{ backgroundColor: "#ffffff" }}
      handleIndicatorStyle={{
        backgroundColor: "#D1D5DB",
        width: 40,
        height: 4,
        borderRadius: 2,
      }}
    >
      <BottomSheetScrollView
        contentContainerStyle={{
          paddingBottom: 20,
        }}
      >
        {/* Professional Header */}
        <View className="flex-row items-center justify-between px-6 py-4 border-b border-gray-100">
          <View className="flex-row items-center gap-3">
            <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center">
              <Brain size={18} color="#3B82F6" />
            </View>
            <Text className="text-xl font-semibold text-gray-900">
              Log Your Mood
            </Text>
          </View>
          <TouchableOpacity onPress={onClose} className="p-2">
            <X size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Explanatory Text */}
        <View className="px-6 py-4">
          <Text className="text-sm text-gray-600 leading-relaxed">
            Track your emotional well-being to better understand patterns and
            improve your mental health over time.
          </Text>
        </View>

        {/* Mood Selection */}
        <View className="mb-8">
          <Label className="px-6 text-gray-700 text-base mb-4 font-medium">
            How are you feeling?
          </Label>
          <View className="px-6">
            <View className="flex-row flex-wrap justify-between gap-y-4">
              {moodOptions.map((mood) => (
                <View key={mood} className="w-[30%] items-center">
                  <TouchableOpacity
                    onPress={() => handleMoodPress(mood)}
                    className={cn(
                      "w-16 h-16 rounded-full items-center justify-center border-2 mb-2",
                      selectedMood === mood
                        ? "border-gray-300 bg-gray-50"
                        : "border-gray-200 bg-white"
                    )}
                    activeOpacity={0.7}
                  >
                    {getMoodIcon(mood, 24, moodColors[mood])}
                  </TouchableOpacity>
                  <Text
                    className={cn(
                      "text-xs capitalize font-medium text-center",
                      selectedMood === mood ? "text-gray-900" : "text-gray-500"
                    )}
                  >
                    {mood}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Error & Success Alerts */}
        {error && (
          <View className="mx-6 mb-4">
            <Alert icon={AlertCircle} className="bg-red-50 border-red-200">
              <AlertTitle className="text-red-800">Error</AlertTitle>
              <AlertDescription className="text-red-600">
                {error}
              </AlertDescription>
            </Alert>
          </View>
        )}
        {logSuccess && (
          <View className="mx-6 mb-4">
            <Alert icon={CheckCircle2} className="bg-green-50 border-green-200">
              <AlertTitle className="text-green-800">Mood Logged!</AlertTitle>
              <AlertDescription className="text-green-600">
                Your feelings have been recorded.
              </AlertDescription>
            </Alert>
          </View>
        )}

        {/* Log Button */}
        {!logSuccess && (
          <View className="mx-6 mb-4">
            <Button
              onPress={handleLogMood}
              disabled={isLogging || !selectedMood}
              className={cn(
                "w-full h-12 rounded-lg",
                selectedMood && !isLogging ? "bg-blue-600" : "bg-gray-400"
              )}
            >
              <Text className="text-white font-semibold text-base">
                {isLogging ? "Logging..." : "Log Mood"}
              </Text>
            </Button>
          </View>
        )}
      </BottomSheetScrollView>
    </BottomSheet>
  );
};

export default LogMoodSheet;
