import { Text, View } from "react-native";
import React, { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import moment from "moment";
import {
  <PERSON><PERSON><PERSON>,
  CheckCircle2,
  TrendingUp,
  Target,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import { Button } from "../ui/button";

const FitnessSummary = () => {
  const today = new Date();
  const todayISO = moment(today).format("YYYY-MM-DD");
  const router = useRouter();

  const workoutsToday = useQuery(api.fitness.getWorkouts, {
    startDate: todayISO,
    endDate: todayISO,
  });

  const weeklyStats = useQuery(api.fitness.getWeeklyStats) || {
    completedWorkouts: 0,
    totalPlanned: 0,
    streakDays: 0,
    caloriesBurned: 0,
  };

  const totalWorkoutsToday = workoutsToday?.length || 0;
  const completedToday = workoutsToday?.filter((w) => w.completed).length || 0;

  const progressValue =
    totalWorkoutsToday > 0 ? (completedToday / totalWorkoutsToday) * 100 : 0;

  interface StatCardProps {
    icon: ReactNode;
    title: string;
    value: string;
    color: string;
  }

  const StatCard = ({ icon, title, value, color }: StatCardProps) => (
    <Card className="flex-1 bg-card border border-border">
      <CardContent className="p-4 flex-row items-center gap-3">
        <View className="p-2 rounded-lg w-10 h-10 items-center justify-center">
          {icon}
        </View>
        <View className="flex-1">
          <Text className="text-sm text-muted-foreground">{title}</Text>
          <Text className="text-xl font-semibold text-foreground">{value}</Text>
        </View>
      </CardContent>
    </Card>
  );

  return (
    <View className="space-y-6">
      {/* Hero Card */}
      <Card className="bg-primary border-0 mb-4">
        <CardContent className="p-6">
          <View className="flex-row justify-between items-center mb-6">
            <View className="flex-1">
              <Text className="text-primary-foreground text-2xl font-bold">
                Fitness Overview
              </Text>
              <Text className="text-primary-foreground/80 text-base mt-1">
                {moment(today).format("dddd, MMMM D")}
              </Text>
            </View>
            <View className="bg-primary-foreground/10 rounded-full p-3">
              <Dumbbell size={24} color="white" />
            </View>
          </View>

          <View className="space-y-4">
            <View className="flex-row items-center gap-2 mb-2">
              <CheckCircle2 size={20} color="#10b981" />
              <Text className="text-primary-foreground font-medium">
                Today: {completedToday}/{totalWorkoutsToday} Workouts (
                {Math.round(progressValue)}%)
              </Text>
            </View>
            <Progress
              value={progressValue}
              className="h-3 bg-primary-foreground/20"
              indicatorClassName="bg-orange-500"
            />
          </View>
        </CardContent>
      </Card>

      {/* Stats Grid */}
      <View className="flex-row gap-4">
        <StatCard
          icon={<TrendingUp size={20} color="#3b82f6" />}
          title="Active Streak"
          value={`${weeklyStats.streakDays} days`}
          color="blue"
        />

        <StatCard
          icon={<Target size={20} color="#10b981" />}
          title="Completed Today"
          value={`${completedToday}/${totalWorkoutsToday}`}
          color="emerald"
        />
      </View>
      <Button
        onPress={() => router.push("/(tabs)/fitness/schedule")}
        className="mt-4"
      >
        <Text className="text-primary-foreground font-medium">
          Today's Workouts
        </Text>
      </Button>
    </View>
  );
};

export default FitnessSummary;
